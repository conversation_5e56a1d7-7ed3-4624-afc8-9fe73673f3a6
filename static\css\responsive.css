/* Responsive Design Styles */

/* Responsive Design */
@media (max-width: 1200px) {
  .main-layout {
    grid-template-columns: 1fr 350px;
  }
}

@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .comments-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
  }

  .comments-section {
    height: auto;
  }

  .comments-list {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .event-content {
    padding: 8px;
    max-width: 100%;
  }

  /* Mobile layout adjustments */
  .page-layout {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .main-content {
    gap: 12px;
    width: 100%;
  }

  .comments-sidebar {
    width: 100%;
    order: 10; /* Move comments to bottom */
  }

  .comments-section {
    max-height: 300px;
    position: static;
    width: 100%;
    padding: 16px;
    margin-top: 8px;
  }

  /* Comprehensive card mobile adjustments */
  .comprehensive-event-card {
    max-height: none;
    margin-bottom: 12px;
  }

  .card-header-section {
    padding: 16px;
  }

  .event-title {
    font-size: 20px;
  }

  .event-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  /* Mobile layout - stack vertically */
  .card-content-layout {
    flex-direction: column;
  }

  .left-column {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
  }

  .right-column {
    border-bottom: none;
  }

  .content-section {
    padding: 16px;
  }

  .section-header h3 {
    font-size: 16px;
  }

  .section-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  /* Mobile section adjustments */
  .location-section .event-map {
    height: 180px;
  }

  .images-section .main-image img {
    height: 200px;
  }

  .comment-item {
    gap: 10px;
  }

  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  .comments-list {
    max-height: 300px;
  }

  /* Image gallery mobile adjustments */
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }

  .image-preview {
    height: 120px;
  }

  .image-actions {
    padding: 8px;
    gap: 6px;
  }

  .image-actions .btn {
    font-size: 10px;
    padding: 3px 6px;
  }

  /* Compact header on mobile */
  .event-header {
    padding: 12px 16px;
    margin-bottom: 16px;
    max-width: none;
    min-width: auto;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .header-main {
    margin-bottom: 8px;
  }

  .event-title {
    font-size: 18px;
  }

  .journey-breadcrumb {
    font-size: 11px;
  }

  .status-badges {
    margin-right: 8px;
  }

  .status-badge {
    padding: 1px 6px;
    font-size: 9px;
  }

  .like-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .menu-btn {
    width: 28px;
    height: 28px;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
  }

  .author-name {
    font-size: 12px;
  }

  .event-date, .update-time {
    font-size: 10px;
  }

  /* Mobile adjustments for manage button */
  .manage-btn-inside {
    bottom: 8px;
    right: 8px;
    padding: 6px 10px;
    font-size: 12px;
    gap: 4px;
  }

  .manage-btn-corner {
    bottom: 8px;
    right: 8px;
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* Ensure dropdown menus are fully opaque on mobile */
  .dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15) !important;
  }
}

@media (max-width: 480px) {
  .event-content {
    padding: 4px;
    max-width: 100%;
  }

  .event-title {
    font-size: 18px;
  }

  .comprehensive-event-card {
    width: 100%;
    margin-bottom: 6px; /* Tighter spacing on very small screens */
  }

  .title-actions-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .title-actions-header .header-actions {
    justify-content: flex-end;
  }

  .meta-section {
    padding: 12px 16px;
  }

  .header-section {
    padding: 12px;
  }

  .content-section {
    padding: 12px;
  }

  .comments-section {
    padding: 12px;
    width: 100%;
    margin-top: 6px;
  }

  /* Compact section styling for very small screens */
  .section-header h3 {
    font-size: 14px;
  }

  .section-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .section-header {
    margin-bottom: 10px;
    padding-bottom: 6px;
  }

  .comments-list {
    max-height: 250px;
  }

  /* Mobile adjustments for no comments state */
  .no-comments-state {
    padding: 20px 12px;
    min-height: 120px;
  }

  .no-comments-icon i {
    font-size: 32px;
  }

  .no-comments-content h4 {
    font-size: 14px;
  }

  .no-comments-content p {
    font-size: 12px;
  }

  /* Ensure full width on very small screens */
  .page-layout,
  .main-content {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .comments-sidebar,
  .comments-section,
  .comprehensive-event-card {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  /* Maintain logical order on very small screens */
  .comprehensive-event-card {
    order: 1;
  }

  .comments-sidebar {
    order: 10;
  }
}
