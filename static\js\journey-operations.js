/**
 * Journey Operations Utility
 * 
 * Provides centralized functions for journey-related operations
 * Moves business logic to backend API calls
 * 
 * Dependencies: flash-messages.js, modal.html
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

class JourneyOperations {
    constructor() {
        this.baseUrl = '/journey/api';
        this.init();
    }

    init() {
        this.bindEventListeners();
    }

    /**
     * Bind event listeners for journey operations
     */
    bindEventListeners() {
        // Follow/Unfollow buttons - handle both button and nested elements
        document.addEventListener('click', (e) => {
            const followButton = e.target.closest('[data-action="toggle-follow"]');
            if (followButton) {
                e.preventDefault();
                e.stopPropagation();
                const journeyId = followButton.dataset.journeyId;
                this.toggleFollow(journeyId, followButton);
            }
        });

        // Visibility change buttons
        document.addEventListener('click', (e) => {
            const visibilityButton = e.target.closest('[data-action="change-visibility"]');
            if (visibilityButton) {
                e.preventDefault();
                e.stopPropagation();
                const journeyId = visibilityButton.dataset.journeyId;
                const newVisibility = visibilityButton.dataset.visibility;
                this.changeVisibility(journeyId, newVisibility);
            }
        });

        // Hide/Unhide buttons (staff only) - handle both button and nested elements
        document.addEventListener('click', (e) => {
            const hiddenButton = e.target.closest('[data-action="toggle-hidden"]');
            if (hiddenButton) {
                e.preventDefault();
                e.stopPropagation();
                const journeyId = hiddenButton.dataset.journeyId;
                this.toggleHiddenStatus(journeyId, hiddenButton);
            }
        });

        // Edit journey buttons
        document.addEventListener('click', (e) => {
            const editButton = e.target.closest('[data-action="edit-journey"]');
            if (editButton) {
                e.preventDefault();
                e.stopPropagation();
                const journeyId = editButton.dataset.journeyId;
                this.openEditModal(journeyId);
            }
        });

        // Delete journey buttons
        document.addEventListener('click', (e) => {
            const deleteButton = e.target.closest('[data-action="delete-journey"]');
            if (deleteButton) {
                e.preventDefault();
                e.stopPropagation();
                const journeyId = deleteButton.dataset.journeyId;
                this.confirmDelete(journeyId);
            }
        });
    }

    /**
     * Toggle follow status for a journey
     */
    async toggleFollow(journeyId, button) {
        try {
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/${journeyId}/toggle-follow`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.success) {
                // Update button text and icon
                const icon = button.querySelector('i');
                const text = button.querySelector('.btn-text');

                if (data.action === 'followed') {
                    // Update icon - adjust spacing based on button type
                    if (button.classList.contains('dropdown-item')) {
                        icon.className = 'bi bi-heart-fill me-2';
                        if (text) text.textContent = 'Unfollow Journey';
                    } else {
                        icon.className = 'bi bi-heart-fill me-1';
                        if (text) text.textContent = 'Following';
                        // Update button classes for standalone button
                        button.classList.add('btn-primary');
                    }
                } else {
                    // Update icon - adjust spacing based on button type
                    if (button.classList.contains('dropdown-item')) {
                        icon.className = 'bi bi-heart me-2';
                        if (text) text.textContent = 'Follow Journey';
                    } else {
                        icon.className = 'bi bi-heart me-1';
                        if (text) text.textContent = 'Follow';
                        // Update button classes for standalone button
                        button.classList.remove('btn-primary');
                    }
                }

                // Show success message
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'success');
                }
            } else {
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'danger');
                }
            }
        } catch (error) {
            console.error('Error toggling follow status:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        } finally {
            button.disabled = false;
        }
    }

    /**
     * Change journey visibility
     */
    async changeVisibility(journeyId, newVisibility) {
        try {
            const response = await fetch(`${this.baseUrl}/${journeyId}/visibility`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    visibility: newVisibility
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update visibility badges
                this.updateVisibilityBadges(newVisibility);
                
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'success');
                }
                
                // Reload page to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'danger');
                }
            }
        } catch (error) {
            console.error('Error changing visibility:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        }
    }

    /**
     * Toggle hidden status (staff only)
     */
    async toggleHiddenStatus(journeyId, button) {
        try {
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/${journeyId}/hidden-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.success) {
                // Update button text
                const text = button.querySelector('.btn-text');
                if (text) {
                    text.textContent = data.is_hidden ? 'Unhide Journey' : 'Hide Journey';
                }

                // Update hidden badge
                this.updateHiddenBadge(data.is_hidden);
                
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'success');
                }
            } else {
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage(data.message, 'danger');
                }
            }
        } catch (error) {
            console.error('Error toggling hidden status:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        } finally {
            button.disabled = false;
        }
    }

    /**
     * Open edit journey modal
     */
    async openEditModal(journeyId) {
        try {
            const response = await fetch(`/journey/private/${journeyId}/edit`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const formHtml = await response.text();

            if (typeof showModal === 'function') {
                showModal('Edit Journey', formHtml, {
                    actionText: 'Save',
                    onAction: async () => {
                        return await this.submitEditForm(journeyId);
                    }
                });
            } else {
                console.error('showModal function not available');
            }
        } catch (error) {
            console.error('Error opening edit modal:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Failed to load edit form. Please try again.', 'danger');
            }
        }
    }

    /**
     * Submit edit form
     */
    async submitEditForm(journeyId) {
        const form = document.getElementById('editJourneyForm');
        if (!form) return false;

        // Validate form
        form.classList.add('was-validated');

        if (!form.checkValidity()) {
            const firstInvalidField = form.querySelector(':invalid');
            if (firstInvalidField) {
                firstInvalidField.focus();
            }
            return false;
        }

        try {
            const formData = new FormData(form);

            const response = await fetch(`/journey/private/${journeyId}/edit`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Check if the response is successful
            if (response.ok) {
                // Parse JSON response
                const result = await response.json();

                if (result.success) {
                    // Show success message immediately (no storage needed)
                    if (typeof window.showFlashMessage === 'function') {
                        window.showFlashMessage(result.message, 'success');
                    }

                    // Close the modal gracefully
                    if (typeof window.closeModal === 'function') {
                        window.closeModal();
                    }

                    // Update the journey details on the page without reload
                    await this.refreshJourneyDetails(journeyId);

                    return true;
                } else {
                    // Show error message immediately
                    if (typeof window.showFlashMessage === 'function') {
                        window.showFlashMessage(result.message || 'Failed to save changes. Please try again.', 'danger');
                    }
                    return false;
                }
            } else {
                // Handle HTTP error response
                const errorText = await response.text();
                console.error('Server error:', response.status, errorText);

                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage('Failed to save changes. Please try again.', 'danger');
                }
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error submitting edit form:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Network error. Please check your connection and try again.', 'danger');
            }
            return false;
        }
    }

    /**
     * Confirm and delete journey
     */
    confirmDelete(journeyId) {
        if (typeof showModal === 'function') {
            showModal(
                'Delete Journey',
                'Are you sure you want to delete this journey? This action cannot be undone.',
                {
                    actionText: 'Delete',
                    onAction: () => {
                        this.deleteJourney(journeyId);
                    }
                }
            );
        } else {
            // Fallback to browser confirm
            if (confirm('Are you sure you want to delete this journey? This action cannot be undone.')) {
                this.deleteJourney(journeyId);
            }
        }
    }

    /**
     * Delete journey
     */
    async deleteJourney(journeyId) {
        try {
            // Submit the delete form if it exists
            const deleteForm = document.getElementById(`deleteJourneyForm_${journeyId}`) || 
                              document.getElementById('deleteJourneyForm');
            
            if (deleteForm) {
                deleteForm.submit();
            } else {
                console.error('Delete form not found');
                if (typeof window.showFlashMessage === 'function') {
                    window.showFlashMessage('Delete form not found. Please refresh and try again.', 'danger');
                }
            }
        } catch (error) {
            console.error('Error deleting journey:', error);
            if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('An error occurred. Please try again.', 'danger');
            }
        }
    }

    /**
     * Update visibility badges in the UI
     */
    updateVisibilityBadges(newVisibility) {
        const badges = document.querySelectorAll('.visibility-badge');
        badges.forEach(badge => {
            badge.className = 'badge visibility-badge rounded-pill py-1 px-3';
            
            if (newVisibility === 'public' || newVisibility === 'published') {
                badge.classList.add('bg-success');
                badge.textContent = newVisibility.charAt(0).toUpperCase() + newVisibility.slice(1);
            } else {
                badge.classList.add('bg-secondary');
                badge.textContent = 'Private';
            }
        });
    }

    /**
     * Update hidden badge in the UI
     */
    updateHiddenBadge(isHidden) {
        const hiddenBadges = document.querySelectorAll('.hidden-badge');

        if (isHidden) {
            if (hiddenBadges.length === 0) {
                // Create hidden badge if it doesn't exist
                const visibilityBadge = document.querySelector('.visibility-badge');
                if (visibilityBadge) {
                    const hiddenBadge = document.createElement('span');
                    hiddenBadge.className = 'badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 hidden-badge';
                    hiddenBadge.textContent = 'Hidden';
                    visibilityBadge.parentNode.insertBefore(hiddenBadge, visibilityBadge.nextSibling);
                }
            }
        } else {
            // Remove hidden badges
            hiddenBadges.forEach(badge => badge.remove());
        }
    }

    /**
     * Refresh journey details on the page using form data
     */
    async refreshJourneyDetails(journeyId) {
        // Get the form data that was just submitted
        const form = document.getElementById('editJourneyForm');
        if (!form) {
            console.error('Edit form not found - this should not happen');
            return;
        }

        const formData = new FormData(form);

        // Update journey title
        const titleElement = document.getElementById('journeyTitle');
        const newTitle = formData.get('title');
        if (titleElement && newTitle) {
            titleElement.textContent = newTitle;
        }

        // Update journey description
        const descriptionElement = document.getElementById('journeyDescription');
        const newDescription = formData.get('description');
        if (descriptionElement && newDescription) {
            descriptionElement.textContent = newDescription;
        }

        // Update start date (format it properly)
        const startDateElement = document.getElementById('journeyStartDate');
        const newStartDate = formData.get('start_date');
        if (startDateElement && newStartDate) {
            // Convert YYYY-MM-DD to a more readable format
            const date = new Date(newStartDate);
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            startDateElement.textContent = date.toLocaleDateString('en-US', options);
        }

        // Update visibility badges
        const newVisibility = formData.get('visibility');
        if (newVisibility) {
            this.updateVisibilityBadges(newVisibility);
        }

        console.log('Journey details updated successfully from form data');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.journeyOperations = new JourneyOperations();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JourneyOperations;
}
