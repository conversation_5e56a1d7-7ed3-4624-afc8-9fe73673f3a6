{% block head %}
<!-- Modular CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/form-layouts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/location-search.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff-permissions.css') }}">

<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/location-operations.js') }}"></script>
{% endblock %}

{% block content %}
<div class="edit-event-modal" id="editEventModal">
  <form method="post" action="{{ url_for('event.update_event', event_id=event.id) }}" enctype="multipart/form-data"
    novalidate id="editEventForm" class="needs-validation modern-form">

    <!-- Hidden fields to store original values for change detection -->
    <input type="hidden" id="original_title" value="{{ event.title }}">
    <input type="hidden" id="original_description" value="{{ event.description }}">
    <input type="hidden" id="original_location" value="{{ event.location_name }}">
    <input type="hidden" id="original_start_datetime"
      value="{{ event.start_datetime.strftime('%Y-%m-%dT%H:%M') if event.start_datetime else '' }}">
    <input type="hidden" id="original_end_datetime"
      value="{{ event.end_datetime.strftime('%Y-%m-%dT%H:%M') if event.end_datetime else '' }}">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-type"></i>
                  Event Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ event.title }}" required minlength="5" maxlength="50"
                  placeholder="Enter event title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="3"
                  placeholder="Describe your event...">{{ event.description }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Date & Time Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-clock section-icon"></i>
              <span class="section-title">Date & Time</span>
              {% if journey.user_id != session.user_id and can_manage_content() %}
              <span class="permission-badge">
                <i class="bi bi-lock-fill"></i>
                Owner Only
              </span>
              {% endif %}
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="startDatetime" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date & Time *
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="startDatetime"
                  name="start_datetime" value="{{ event.start_datetime.strftime('%Y-%m-%dT%H:%M') if event.start_datetime else '' }}"
                  {% if journey.user_id != session.user_id and can_manage_content() %}disabled{% else %}required{% endif %} />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              <div class="form-group">
                <label for="endDatetime" class="modern-label">
                  <i class="bi bi-calendar-check"></i>
                  End Date & Time
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="endDatetime" name="end_datetime"
                  value="{{ event.end_datetime.strftime('%Y-%m-%dT%H:%M') if event.end_datetime else ''}}"
                  {% if journey.user_id != session.user_id and can_manage_content() %}disabled{% endif %} />
                <div class="invalid-feedback">End date cannot be before start date.</div>
                {% if journey.user_id != session.user_id and can_manage_content() %}
                <div class="permission-notice compact">
                  <i class="bi bi-info-circle"></i>
                  Only the Journey owner can edit the date and time of an event.
                </div>
                {% endif %}
              </div>
            </div>
          </div>


        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Current Location Display (Initial State) -->
          <div class="form-section compact" id="currentLocationSection">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Current Location</span>
              {% if journey.user_id != session.user_id and can_manage_content() %}
              <span class="permission-badge staff-edit">
                <i class="bi bi-shield-check"></i>
                Staff Edit
              </span>
              {% endif %}
            </div>

            <div class="current-location-display">
              <div class="current-location-info">
                <div class="location-header">
                  <i class="bi bi-geo-alt-fill text-primary"></i>
                  <span class="location-name">{{ event.location_name }}</span>
                </div>
              </div>
            </div>

            <!-- Location Actions -->
            <div class="change-location-actions">
              {% if journey.user_id == session.user_id %}
              <!-- Owner can change full location (coordinates + name) -->
              <button type="button" id="changeLocationBtn" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-pencil"></i>
                Change Location
              </button>
              {% elif can_manage_content() %}
              <!-- Content managers can only edit location name -->
              <button type="button" id="editLocationNameBtn" class="btn btn-outline-warning btn-sm">
                <i class="bi bi-pencil-square"></i>
                Edit Location Name
              </button>
              {% endif %}
            </div>

            {% if journey.user_id != session.user_id and can_manage_content() %}
            <div class="permission-notice compact staff-notice">
              <i class="bi bi-info-circle"></i>
              As content manager, you can edit the location name but not the map coordinates.
            </div>
            {% endif %}
          </div>

          <!-- Staff Location Name Edit Section (Hidden Initially) -->
          {% if journey.user_id != session.user_id and can_manage_content() %}
          <div class="form-section compact staff-section" id="staffLocationEditSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-pencil-square section-icon"></i>
              <span class="section-title">Edit Location Name</span>
              <span class="staff-badge">
                <i class="bi bi-shield-check"></i>
                Staff Only
              </span>
            </div>

            <div class="form-group">
              <label for="staffLocationName" class="modern-label">
                <i class="bi bi-tag"></i>
                Location Name *
              </label>
              <input type="text" class="modern-input" id="staffLocationName" name="location"
                value="{{ event.location_name }}" required />
              <div class="invalid-feedback" id="staffLocationNameError">Location name is required.</div>
            </div>

            <div class="form-group">
              <label for="staffLocationScope" class="modern-label">
                <i class="bi bi-gear"></i>
                Update Scope *
              </label>
              <select class="modern-input" id="staffLocationScope" name="staff_location_scope" required>
                <option value="all_events">Update location everywhere (Recommended)</option>
                <option value="this_event_only">Create new location for this event only</option>
              </select>
              <div class="input-help" id="staffLocationScopeHelp">
                <i class="bi bi-info-circle"></i>
                <span id="scopeHelpText">This will change the name for ALL events using this location.</span>
              </div>
            </div>

            <div class="staff-edit-actions">
              <button type="button" id="cancelLocationEditBtn" class="btn btn-outline-secondary btn-sm">
                <i class="bi bi-x"></i>
                Cancel
              </button>
            </div>
          </div>
          {% endif %}

          <!-- Staff Location Change Choice Modal -->
          {% if journey.user_id != session.user_id and can_manage_content() %}
          <div class="modal fade" id="staffLocationChoiceModal" tabindex="-1" aria-labelledby="staffLocationChoiceModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="staffLocationChoiceModalLabel">
                    <i class="bi bi-geo-alt me-2"></i>
                    Location Name Change Options
                  </h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>You're changing the location name:</strong>
                    <br>
                    From: "<span id="originalLocationName"></span>"
                    <br>
                    To: "<span id="newLocationName"></span>"
                  </div>

                  <div class="location-choice-options">
                    <div class="form-check mb-3">
                      <input class="form-check-input" type="radio" name="locationChangeOption" id="updateExistingLocation" value="update" checked>
                      <label class="form-check-label" for="updateExistingLocation">
                        <strong>Update location everywhere (Recommended)</strong>
                        <div class="choice-description">
                          <i class="bi bi-arrow-repeat me-1"></i>
                          This will change the name for <strong>ALL events</strong> using this location.
                          <span id="affectedEventsCount"></span>
                        </div>
                        <div class="choice-benefits">
                          <small class="text-success">
                            <i class="bi bi-check-circle me-1"></i>
                            Fixes inappropriate name everywhere • Maintains data consistency
                          </small>
                        </div>
                      </label>
                    </div>

                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="locationChangeOption" id="createNewLocation" value="create">
                      <label class="form-check-label" for="createNewLocation">
                        <strong>Create new location for this event only</strong>
                        <div class="choice-description">
                          <i class="bi bi-plus-circle me-1"></i>
                          Other events will keep the original name. Creates a new location in database.
                        </div>
                        <div class="choice-benefits">
                          <small class="text-warning">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            Only affects this event • Other events still have original name
                          </small>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-1"></i>
                    Cancel
                  </button>
                  <button type="button" class="btn btn-primary" id="confirmLocationChange">
                    <i class="bi bi-check me-1"></i>
                    Apply Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- Current Location Map Preview (Initial State) -->
          <div class="form-section map-section" id="currentMapSection">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Location Map</span>
            </div>

            <div class="location-content">
              <!-- Non-Interactive Map Preview -->
              <div class="map-container desktop-optimized">
                <div id="currentMap" class="modern-map map-preview-only"></div>
              </div>
            </div>
          </div>

          <!-- Location Search Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section compact" id="locationSearchSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-search section-icon"></i>
              <span class="section-title">Search Location</span>
            </div>

            <div class="form-group">
              <label for="locationSearch" class="modern-label">
                <i class="bi bi-search"></i>
                Search for Location *
              </label>
              <div class="location-search-container">
                <input type="text" class="modern-input" id="locationSearch"
                  data-location-search
                  placeholder="Type location name or address..." />
                <div id="locationSuggestions" class="location-dropdown d-none"></div>
              </div>
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Search for existing locations or new addresses
              </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="location-results" style="display: none;">
              <div class="results-header">
                <h6 class="mb-0">Search Results</h6>
              </div>
              <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
              </div>
            </div>
          </div>

          <!-- Selected Location Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section compact" id="selectedLocationSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-geo-alt section-icon"></i>
              <span class="section-title">Selected Location</span>
            </div>

            <div class="selected-location-info">
              <div class="form-group">
                <label for="location" class="modern-label">
                  <i class="bi bi-tag"></i>
                  Location Name *
                </label>
                <input type="text" class="modern-input" id="location" name="location"
                  value="{{ event.location_name }}" required readonly />
                <div class="invalid-feedback">Location is required.</div>
              </div>

              <div class="location-actions mt-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" id="backToSearchBtn">
                  <i class="bi bi-arrow-left"></i> Back to Search
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary" id="editMapLocationBtn"
                  style="display: none;">
                  <i class="bi bi-geo-alt"></i> Change Map Location
                </button>
              </div>
            </div>
          </div>

          <!-- Map Section (Hidden Initially, Same as Create Event Modal) -->
          <div class="form-section map-section" id="mapSection" style="display: none;">
            <div class="section-header">
              <i class="bi bi-map section-icon"></i>
              <span class="section-title">Map Preview</span>
            </div>

            <div class="location-content">
              <!-- Interactive Map -->
              <div class="map-container desktop-optimized">
                <div id="map" class="modern-map {% if journey.user_id != session.user_id and can_manage_content() %}map-disabled{% endif %}"></div>
              </div>

              <!-- Map Search (for new locations) -->
              <div id="mapSearchGroup" class="form-group mt-3" style="display: none;">
                <label for="mapSearch" class="modern-label">
                  <i class="bi bi-search"></i>
                  Search Address on Map
                </label>
                <div class="map-search-container">
                  <input type="text" class="modern-input" id="mapSearch"
                         data-map-search
                         placeholder="Search for address..." />
                  <div id="mapSuggestions" class="location-dropdown d-none"></div>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Search for address and click on map to set location
                </div>

                <!-- Coordinates Status (Hidden from users) -->
                <div id="coordinatesStatus" class="mt-2" style="display: none !important;">
                  <div class="alert alert-success py-2 px-3 mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="coordinatesText">Location coordinates set</span>
                  </div>
                </div>

                <!-- New Location Name Input -->
                <div id="newLocationNameGroup" class="mt-3" style="display: none;">
                  <label for="newLocationName" class="modern-label">
                    <i class="bi bi-tag"></i>
                    Name for New Location *
                  </label>
                  <input type="text" class="modern-input" id="newLocationName"
                    placeholder="Enter unique name for this location" />
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    This name will be validated and created when you submit the event
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Staff Edit Reason Section (if applicable) -->
          {% if journey.user_id != session.user_id and session.get('role') in ['editor', 'admin', 'support_tech'] %}
          <div class="form-section staff-section compact">
            <div class="section-header">
              <i class="bi bi-shield-check section-icon"></i>
              <span class="section-title">Staff Edit Reason</span>
              <span class="required-badge">Required</span>
            </div>

            <div class="form-group">
              <label for="edit_reason" class="modern-label">
                <i class="bi bi-chat-square-text"></i>
                Reason for Edit *
              </label>
              <textarea class="modern-textarea" id="edit_reason" name="edit_reason" rows="3" required
                placeholder="Please provide a reason for this edit..."></textarea>
              <div class="invalid-feedback">Staff must provide a reason for editing user content</div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Hidden coordinate fields -->
      <input type="hidden" id="latitude" name="latitude"
        value="{{ event.location_latitude if event.location_latitude else '' }}">
      <input type="hidden" id="longitude" name="longitude"
        value="{{ event.location_longitude if event.location_longitude else '' }}">

      <!-- Hidden input to keep track of deleted images -->
      <input type="hidden" name="deleted_images" id="deletedImages" value="">
    </div>
  </form>

  <style>
    /* Event Edit Specific Styles */

    /* Staff location scope styling */
    #staffLocationScope {
      cursor: pointer !important;
      background-color: #ffffff !important;
      color: #2d3748 !important;
      border-color: #e2e8f0 !important;
      pointer-events: auto !important;
    }

    #staffLocationScopeHelp .text-primary {
      color: #0d6efd !important;
      font-weight: 500;
    }

    #staffLocationScopeHelp .text-warning {
      color: #fd7e14 !important;
      font-weight: 500;
    }

    /* Map preview only (non-interactive) */
    .map-preview-only {
      pointer-events: none;
      position: relative;
    }

    .map-preview-only::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      pointer-events: none;
    }
  </style>

  <script>
    // Initialize Event Edit Form
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('editEventForm');
      if (!form) return;

      // Initialize enhanced form validation
      if (window.EnhancedFormValidation) {
        window.EnhancedFormValidation.initializeModernForm(form, {
          validateOnInput: true,
          validateOnBlur: true,
          showSuccessStates: true
        });
      }

      // Initialize location operations for staff editing
      if (window.LocationOperations) {
        window.LocationOperations.initialize({
          searchInput: '#locationSearch',
          resultsContainer: '#searchResults',
          selectedSection: '#selectedLocationSection',
          mapContainer: '#map',
          onLocationSelected: function(location) {
            console.log('Location selected for edit:', location);
          }
        });
      }

      console.log('✅ Event edit form initialized');
    });
  </script>
</div>
{% endblock %}