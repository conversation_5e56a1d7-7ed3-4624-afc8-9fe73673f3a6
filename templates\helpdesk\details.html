{% extends "base.html" %}

{% block title %}Help desk - Footprints{% endblock %}

{% block content %}
<div class="container">
    <!-- Back button -->
    <a href="javascript:void(0)" onclick="smartBack()"
        class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span id="backButtonText">Back</span>
    </a>

    <div class="row g-4" id="ticketContainer">
        <div class="col-md-4">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-4">
                        <h1 class="fs-3 fw-bold mb-1">Ticket Details</h1>
                        <!-- Hide dropdown menu for final status tickets (resolved, approved, rejected) -->
                        <div class="dropdown" {% if can_administrate() and ticket.status not in ['resolved', 'approved'
                            , 'rejected' ] %}{% else %}style='display: none;' {% endif %}>
                            <button class="btn btn-sm btn-light rounded-circle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                {% if not ticket.assigned_to %}
                                <!-- Take button for unassigned tickets -->
                                <li>
                                    <a class="dropdown-item modal-trigger" href="#" data-action="take"
                                        data-ticket-id="{{ ticket.id }}">
                                        <i class="bi bi-person-plus-fill me-2 text-success"></i>Take Ticket
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                {% endif %}
                                <li>
                                    <a class="dropdown-item modal-trigger" href="#" data-action="assign"
                                        data-ticket-id="{{ ticket.id }}">
                                        <i class="bi bi-person-plus me-2 text-dark"></i>{% if ticket.assigned_to
                                        %}Reassign Ticket{% else %}Assign Ticket{% endif %}
                                    </a>
                                </li>
                                {% if ticket.assigned_to and ticket.assigned_to == session.user_id and
                                ticket.request_type != 'appeal' %}
                                <li>
                                    <a class="dropdown-item modal-trigger" href="#" data-action="change"
                                        data-ticket-id="{{ ticket.id }}">
                                        <i class="bi bi-pencil-square me-2 text-dark"></i>Change Status
                                    </a>
                                </li>
                                {% endif %}
                                {% if ticket.assigned_to and ticket.assigned_to == session.user_id %}
                                <li>
                                    <a class="dropdown-item modal-trigger" href="#" data-action="drop"
                                        data-ticket-id="{{ ticket.id }}">
                                        <i class="bi bi-person-dash-fill me-2 text-warning"></i>Drop Ticket
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                    <div class="mb-3">
                        <span
                            class="badge rounded-pill px-3 py-2 {% if ticket.status == 'new' %}bg-primary-subtle text-primary{% elif ticket.status == 'open' %}bg-success-subtle text-success{% elif ticket.status == 'stalled' %}bg-warning-subtle text-warning{% elif ticket.status == 'resolved' %}bg-info-subtle text-info{% elif ticket.status == 'approved' %}bg-success-subtle text-success{% elif ticket.status == 'rejected' %}bg-secondary-subtle text-secondary{% endif %}">
                            {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>New{% elif ticket.status ==
                            'open' %}<i class="bi bi-unlock me-1"></i>Open{% elif ticket.status == 'stalled' %}<i
                                class="bi bi-pause me-1"></i>Stalled{% elif ticket.status == 'resolved' %}<i
                                class="bi bi-check2-circle me-1"></i>Resolved{% elif ticket.status == 'approved' %}<i
                                class="bi bi-check-lg me-1"></i>Approved{% elif ticket.status == 'rejected' %}<i
                                class="bi bi-x-circle me-1"></i>Rejected{% endif %}
                        </span>
                        {% if ticket.assigned_to %}
                        {% set assigned_user = ticket.get('assigned_username', ticket.staff_name) %}
                        <span class="badge rounded-pill {{ get_role_badge_class('support_tech') }} px-3 py-2 ms-2">
                            <i class="{{ get_role_icon('support_tech') }} me-1"></i>{{ assigned_user }}
                        </span>
                        {% endif %}
                    </div>
                    <div class="d-flex align-items-center mb-4">
                        {% if ticket.profile_image %}
                        <img style="object-fit: cover;width: 50px;height:50px;"
                            src="{{ url_for('static', filename='uploads/profile_images/' + ticket.profile_image) }}"
                            class="rounded-circle me-3" alt="User Avatar"
                            onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                        {% else %}
                        <img style="object-fit: cover;width: 50px;height:50px;"
                            src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                            class="rounded-circle me-3" alt="User Avatar">
                        {% endif %}
                        <div>
                            <h6 class="mb-0 fw-semibold">{{ ticket.username }}</h6>
                            <small class="text-muted"><i class="bi bi-clock me-1"></i>Created: {{
                                ticket.created_at|datetime }}</small>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-uppercase text-muted small fw-bold">Title</h6>
                        <p class="text-dark fs-5 fw-medium mb-0">{{ ticket.subject }}</p>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
                        <p class="mb-0" style="white-space: pre-wrap;">{{ ticket.description }}</p>
                    </div>

                    <!-- Show admin response for appeals -->
                    {% if ticket.request_type == 'appeal' and ticket.admin_response %}
                    <div class="mb-4">
                        <h6 class="text-uppercase text-muted small fw-bold">
                            {% if ticket.status == 'approved' %}
                            <i class="bi bi-check-circle text-success me-1"></i>Staff Response
                            {% elif ticket.status == 'rejected' %}
                            <i class="bi bi-x-circle text-danger me-1"></i>Rejection Reason
                            {% else %}
                            <i class="bi bi-chat-square-text me-1"></i>Staff Response
                            {% endif %}
                        </h6>
                        <div
                            class="p-3 rounded-3 {% if ticket.status == 'approved' %}bg-success-subtle border border-success-subtle{% elif ticket.status == 'rejected' %}bg-danger-subtle border border-danger-subtle{% else %}bg-light border{% endif %}">
                            <p
                                class="mb-0 {% if ticket.status == 'approved' %}text-success-emphasis{% elif ticket.status == 'rejected' %}text-danger-emphasis{% endif %}">
                                {{ ticket.admin_response }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Appeal Processing Section -->
                    {% if ticket.request_type == 'appeal' and (ticket.appeal_type == 'hidden_journey' or
                    ticket.appeal_type == 'sharing_block' or ticket.appeal_type == 'ban') and can_manage_content() %}
                    <div class="border-top pt-4">
                        <h6 class="text-uppercase text-muted small fw-bold mb-3">Appeal Processing</h6>

                        {% if ticket.status in ['new', 'open'] %}
                        {% if not ticket.assigned_to %}
                        <!-- Appeal not assigned -->
                        <div class="alert alert-warning rounded-3 mb-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-exclamation-triangle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Assignment Required</h6>
                                    <p class="mb-0 small">This appeal must be assigned to a content manager before it
                                        can be processed. Use "Take Ticket" or "Assign Ticket" above.</p>
                                </div>
                            </div>
                        </div>
                        {% elif ticket.assigned_to == session.user_id %}
                        <!-- Appeal assigned to current user -->
                        <div class="alert alert-info rounded-3 mb-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">
                                        {% if ticket.appeal_type == 'hidden_journey' %}
                                        Journey Appeal
                                        {% elif ticket.appeal_type == 'sharing_block' %}
                                        Blocked User Appeal
                                        {% elif ticket.appeal_type == 'ban' %}
                                        Ban Appeal
                                        {% else %}
                                        Appeal
                                        {% endif %}
                                    </h6>
                                    <p class="mb-0 small">
                                        {% if ticket.appeal_type == 'hidden_journey' %}
                                        This appeal is assigned to you. You can approve to unhide the journey or reject
                                        with a reason.
                                        {% elif ticket.appeal_type == 'sharing_block' %}
                                        This appeal is assigned to you. You can approve to unblock the user or reject
                                        with a reason.
                                        {% elif ticket.appeal_type == 'ban' %}
                                        This appeal is assigned to you. You can approve to unban the user or reject with
                                        a reason.
                                        {% else %}
                                        This appeal is assigned to you. Review and decide whether to approve or reject
                                        it.
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success btn-sm rounded-pill appeal-action-btn"
                                data-action="approve" data-appeal-id="{{ ticket.id }}">
                                <i class="bi bi-check-lg me-1"></i>Approve Appeal
                            </button>
                            <button type="button" class="btn btn-danger btn-sm rounded-pill appeal-action-btn"
                                data-action="reject" data-appeal-id="{{ ticket.id }}">
                                <i class="bi bi-x-lg me-1"></i>Reject Appeal
                            </button>
                        </div>
                        {% else %}
                        <!-- Appeal assigned to someone else -->
                        <div class="alert alert-secondary rounded-3 mb-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-person-check fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Appeal Assigned</h6>
                                    <p class="mb-0 small">This appeal is assigned to {{ ticket.get('assigned_username',
                                        ticket.staff_name) }}. Only the assigned staff member can process this appeal.
                                    </p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% elif ticket.status == 'approved' %}
                        <div class="alert alert-success rounded-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-check-circle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Appeal Approved</h6>
                                    <p class="mb-0 small">This appeal has been approved and the journey has been
                                        unhidden.</p>
                                </div>
                            </div>
                        </div>
                        {% elif ticket.status == 'rejected' %}
                        <div class="alert alert-secondary rounded-3">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-x-circle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Appeal Rejected</h6>
                                    <p class="mb-0 small">This appeal has been reviewed and rejected.</p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <!-- Comments panel (우측) -->
        <div class="col-md-8">
            <div class="card shadow-sm border-0 rounded-3 h-100">
                <div class="card-body d-flex flex-column h-100">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="fs-3 fw-bold mb-0">Comments <span class="badge bg-dark rounded-pill">{{
                                ticket.replies|length }}</span></h2>
                    </div>
                    <div class="flex-grow-1 d-flex align-items-center justify-content-center">
                        {% if ticket.replies %}
                        <div class="w-100">
                            {% for reply in ticket.replies %}
                            <div class="d-flex mb-3">
                                {% if reply.profile_image %}
                                <img style="object-fit: cover;width: 50px;height:50px;"
                                    src="{{ url_for('static', filename='uploads/profile_images/' + reply.profile_image) }}"
                                    class="rounded-circle me-3" alt="User Avatar"
                                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                                {% else %}
                                <img style="object-fit: cover;width: 50px;height:50px;"
                                    src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                                    class="rounded-circle me-3" alt="User Avatar">
                                {% endif %}
                                <div>
                                    <h5 class="card-title mb-0 text-start fs-6">{{reply.username}}</h5>
                                    <p class="text-muted fs-6 mb-1"><i class="bi bi-clock me-1"></i>{{
                                        reply.created_at|datetime }}</p>
                                    <p class="card-text text-start">{{reply.content}}</p>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="empty-state text-center w-100">
                            <div class="empty-state-icon mb-4">
                                <i class="bi bi-chat-left-dots" style="font-size: 4rem; color: #d1d1d1;"></i>
                            </div>
                            <h3 class="mb-3 fw-bold">No comments yet</h3>
                            <p class="text-muted mb-4">Be the first to leave a comment!</p>
                        </div>
                        {% endif %}
                    </div>
                    {% if ticket.status in ['resolved', 'approved', 'rejected'] or (ticket.assigned_to is none and
                    ticket.user_id != session.user_id) %}
                    <div class="alert alert-info rounded-3 mt-4">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                            <div>
                                {% if ticket.status in ['resolved', 'approved', 'rejected'] %}
                                <h6 class="fw-bold mb-1">Comments Disabled</h6>
                                <p class="mb-0 small">This ticket has been {{ ticket.status }} and no longer accepts new
                                    comments.</p>
                                {% elif ticket.assigned_to is none and ticket.user_id != session.user_id %}
                                <h6 class="fw-bold mb-1">Comments Restricted</h6>
                                <p class="mb-0 small">Only the ticket creator and assigned staff can add comments to
                                    this ticket.</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="mb-3 mt-4">
                        <form action="{{url_for('helpdesk.add_reply')}}" id="post_comment" method="post">
                            <input type="hidden" name="ticket_id" value="{{ticket.id}}">
                            <input type="hidden" name="user_id" value="{{session['user_id']}}">
                            <textarea class="form-control mb-2 rounded" id="content" rows="3" placeholder="Your Comment"
                                name="content" required style="border-radius: 15px;"></textarea>
                            <div class="text-end">
                                <button type="submit" class="btn btn-dark rounded-pill">Add a Comment</button>
                            </div>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
<div id="assignTicketFormTemplate" style="display:none;">
    <form id="assignTicketForm" method="POST" action="{{ url_for('helpdesk.assign_ticket') }}">
        <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="mb-3">
            <label for="staff_id" class="form-label">Assign to Content Manager</label>
            <select class="form-select" name="staff_id" required style="max-width: 500px;">
                <option value="" disabled {% if not ticket.assigned_to %}selected{% endif %}>Select content manager
                </option>
                {% for staff in staff_list %}
                {# Only show content managers (editor, support_tech, admin) and exclude current user #}
                {% if staff.role in ['editor', 'support_tech', 'admin'] and staff.id != session.user_id %}
                <option value="{{staff.id}}" {% if staff.id|string==ticket.assigned_to|string %}selected{% endif %}>
                    <span class="badge {{ get_role_badge_class(staff.role) }}">
                        <i class="{{ get_role_icon(staff.role) }}"></i>
                    </span>
                    {{staff.username}} ({{staff.role|title}})
                </option>
                {% endif %}
                {% endfor %}
            </select>
            <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>
                Only content managers can be assigned tickets. Use "Take Ticket" to assign to yourself.
            </div>
        </div>
    </form>
</div>
<div id="changeStatusFormTemplate" style="display:none;">
    <form id="changeStatusForm" method="POST" action="{{ url_for('helpdesk.change_ticket_status') }}">
        <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="mb-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-control" name="status">
                <option value="open" {% if ticket.status|string=='open' %}selected{% endif %}>open</option>
                <option value="stalled" {% if ticket.status|string=='stalled' %}selected{% endif %}>stalled</option>
                <option value="resolved" {% if ticket.status|string=='resolved' %}selected{% endif %}>resolved</option>
                <!--
                <option value="approved" {% if ticket.status|string == 'approved' %}selected{% endif %}>approved</option>
                <option value="rejected" {% if ticket.status|string == 'rejected' %}selected{% endif %}>rejected</option>
                -->
            </select>
        </div>
    </form>
</div>
<div id="takeTicketFormTemplate" style="display:none;">
    <form id="takeTicketForm" method="POST" action="{{ url_for('helpdesk.take_ticket') }}">
        <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="alert alert-success rounded-3 mb-3">
            <div class="d-flex align-items-start">
                <i class="bi bi-person-plus-fill fs-5 me-3 mt-1"></i>
                <div>
                    <h6 class="fw-bold mb-1">Take Ticket</h6>
                    <p class="mb-0 small">This will assign the ticket to you and change its status to "open".</p>
                </div>
            </div>
        </div>
        <p>Are you sure you want to take this ticket?</p>
    </form>
</div>

<div id="dropTicketFormTemplate" style="display:none;">
    <form id="dropTicketForm" method="POST" action="{{ url_for('helpdesk.drop_ticket') }}">
        <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="alert alert-warning rounded-3 mb-3">
            <div class="d-flex align-items-start">
                <i class="bi bi-person-dash-fill fs-5 me-3 mt-1"></i>
                <div>
                    <h6 class="fw-bold mb-1">Drop Ticket</h6>
                    <p class="mb-0 small">This will unassign the ticket and return it to the queue for other staff to
                        take.</p>
                </div>
            </div>
        </div>
        <p>Are you sure you want to drop this ticket?</p>
    </form>
</div>

<!-- Appeal Processing Modal Templates -->
<div id="approveAppealFormTemplate" style="display:none;">
    <form id="approveAppealForm" method="POST" action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
        <input type="hidden" name="action" value="approve">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="alert alert-success rounded-3 mb-3">
            <div class="d-flex align-items-start">
                <i class="bi bi-check-circle fs-5 me-3 mt-1"></i>
                <div>
                    <h6 class="fw-bold mb-1">Approve Appeal</h6>
                    <p class="mb-0 small">
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        This will unhide the journey and make it visible to other users again.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        This will unblock the user and allow them to share journeys again.
                        {% elif ticket.appeal_type == 'ban' %}
                        This will unban the user and allow them to log in again.
                        {% else %}
                        This will approve the appeal and take the appropriate action.
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="approve_response" class="form-label">Response to User (Optional)</label>
            <textarea class="form-control" id="approve_response" name="response" rows="3"
                placeholder="Your appeal has been approved and your journey has been made visible again."></textarea>
            <div class="form-text">This message will be sent to the user as a notification.</div>
        </div>
    </form>
</div>

<div id="rejectAppealFormTemplate" style="display:none;">
    <form id="rejectAppealForm" method="POST" action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
        <input type="hidden" name="action" value="reject">
        <!-- Preserve back parameter for navigation -->
        {% if request.args.get('back') %}
        <input type="hidden" name="back" value="{{ request.args.get('back') }}">
        {% endif %}
        <div class="alert alert-danger rounded-3 mb-3">
            <div class="d-flex align-items-start">
                <i class="bi bi-x-circle fs-5 me-3 mt-1"></i>
                <div>
                    <h6 class="fw-bold mb-1">Reject Appeal</h6>
                    <p class="mb-0 small">
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        The journey will remain hidden. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        The user will remain blocked from sharing. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'ban' %}
                        The user will remain banned from the system. Please provide a clear explanation.
                        {% else %}
                        The appeal will be rejected. Please provide a clear explanation.
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        <div class="mb-3">
            <label for="reject_response" class="form-label">Reason for Rejection <span
                    class="text-danger">*</span></label>
            <textarea class="form-control" id="reject_response" name="response" rows="4" required
                placeholder="Please explain why the appeal is being rejected and what the user can do to address the issues."></textarea>
            <div class="form-text">This explanation will be sent to the user. Be specific and constructive.</div>
        </div>
    </form>
</div>
<style>
    .empty-state {
        min-height: 70vh;
        max-width: 400px;
        padding: 2rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .empty-state-icon {
        margin: 0 auto;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<script>
    // Global variables for role checking
    const canAdministrate = {% if can_administrate() %}true{% else %} false{% endif %};
    const isLoggedIn = {% if g.current_user %}true{% else %} false{% endif %};

    // Simple and reliable back button function for helpdesk detail page
    function smartBack() {
        // Method 1: Check for explicit back URL parameter (most reliable)
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        if (backUrl) {
            try {
                const decodedUrl = decodeURIComponent(backUrl);
                // Only allow internal URLs for security
                if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
                    window.location.href = decodedUrl;
                    return;
                }
            } catch (e) {
                // Invalid URL, continue to next method
            }
        }

        // Method 2: Check if user came from notifications
        const referrer = document.referrer;
        if (referrer && (referrer.includes('/notifications') || referrer.includes('notification'))) {
            // User came from notifications
            if (isLoggedIn) {
                if (referrer.includes('/notifications/all')) {
                    // User came from notification list page, go back to notifications
                    window.location.href = '{{ url_for("main.view_all_notifications") }}';
                } else {
                    // User came from navbar notifications, default to their helpdesk tickets
                    window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                }
            } else {
                window.location.href = '{{ url_for("main.get_landing_page") }}';
            }
            return;
        }

        // Method 3: Check if ticket has final status (likely after appeal processing)
        // In this case, prioritize going back to ticket list over history.back()
        const ticketStatus = '{{ ticket.status }}';
        if (ticketStatus === 'approved' || ticketStatus === 'rejected' || ticketStatus === 'resolved') {
            if (isLoggedIn) {
                if (canAdministrate) {
                    window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
                } else {
                    window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                }
            } else {
                window.location.href = '{{ url_for("main.get_landing_page") }}';
            }
            return;
        }

        // Method 4: Check if this is likely after a ticket management action (drop/take)
        // Look for signs that the page was refreshed after an action
        if (canAdministrate && referrer && referrer.includes(window.location.pathname)) {
            // The referrer is the same page, suggesting a form submission and redirect
            // This commonly happens after drop/take actions
            window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
            return;
        }

        // Method 5: Simple history back (works most of the time for active tickets)
        if (window.history.length > 1) {
            window.history.back();
            return;
        }

        // Method 6: Fallback based on user role
        if (isLoggedIn) {
            if (canAdministrate) {
                window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
            } else {
                window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
            }
        } else {
            window.location.href = '{{ url_for("main.get_landing_page") }}';
        }
    }

    // Simple back button text update
    function updateBackButtonText() {
        const backButtonText = document.getElementById('backButtonText');

        // Check for back URL parameter first
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        if (backUrl) {
            // Try to determine context from back URL
            if (backUrl.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else if (backUrl.includes('/helpdesk')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            } else if (backUrl.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
            } else {
                backButtonText.textContent = 'Back';
            }
            return;
        }

        // Check if user came from notifications
        const referrer = document.referrer;
        if (referrer && (referrer.includes('/notifications') || referrer.includes('notification'))) {
            if (referrer.includes('/notifications/all')) {
                backButtonText.textContent = 'Back to Notifications';
            } else {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            }
            return;
        }

        // Check if ticket has final status or likely after ticket management action
        const ticketStatus = '{{ ticket.status }}';
        if (ticketStatus === 'approved' || ticketStatus === 'rejected' || ticketStatus === 'resolved') {
            if (canAdministrate) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            }
            return;
        }

        // Check if this is likely after a ticket management action (same logic as smartBack)
        if (canAdministrate && referrer && referrer.includes(window.location.pathname)) {
            backButtonText.textContent = 'Back to Helpdesk Management';
            return;
        }

        // Fallback to simple referrer check
        if (referrer && referrer.includes('/helpdesk/manage')) {
            backButtonText.textContent = 'Back to Helpdesk Management';
        } else if (referrer && referrer.includes('/helpdesk')) {
            backButtonText.textContent = 'Back to My Helpdesk Tickets';
        } else if (canAdministrate) {
            backButtonText.textContent = 'Back to Helpdesk Management';
        } else {
            backButtonText.textContent = 'Back to My Helpdesk Tickets';
        }
    }



    document.addEventListener('DOMContentLoaded', function () {
        // Update back button text on page load
        updateBackButtonText();

        // Existing modal functionality
        document.querySelectorAll('.modal-trigger').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                let formHtml, title, actionText, actionClass;
                if (action === 'assign') {
                    formHtml = document.getElementById('assignTicketFormTemplate').innerHTML;
                    title = '{{ ticket.assigned_to and "Reassign Ticket" or "Assign Ticket" }}';
                    actionText = 'Assign';
                    actionClass = 'btn-primary';
                } else if (action === 'change') {
                    formHtml = document.getElementById('changeStatusFormTemplate').innerHTML;
                    title = 'Change Status';
                    actionText = 'Update';
                    actionClass = 'btn-primary';
                } else if (action === 'take') {
                    formHtml = document.getElementById('takeTicketFormTemplate').innerHTML;
                    title = 'Take Ticket';
                    actionText = 'Take';
                    actionClass = 'btn-success';
                } else if (action === 'drop') {
                    formHtml = document.getElementById('dropTicketFormTemplate').innerHTML;
                    title = 'Drop Ticket';
                    actionText = 'Drop';
                    actionClass = 'btn-warning';
                }
                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function () {
                        const form = document.querySelector('#commonModal form');
                        if (form) form.submit();
                    }
                });
            });
        });

        // Appeal processing functionality
        document.querySelectorAll('.appeal-action-btn').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                const appealId = this.getAttribute('data-appeal-id');

                let formHtml, title, actionText, actionClass;

                if (action === 'approve') {
                    formHtml = document.getElementById('approveAppealFormTemplate').innerHTML;
                    title = 'Approve Appeal';
                    actionText = 'Approve Appeal';
                    actionClass = 'btn-success';
                } else if (action === 'reject') {
                    formHtml = document.getElementById('rejectAppealFormTemplate').innerHTML;
                    title = 'Reject Appeal';
                    actionText = 'Reject Appeal';
                    actionClass = 'btn-danger';
                }

                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function () {
                        const form = document.querySelector('#commonModal form');
                        if (form) {
                            // Validate required fields for reject action
                            if (action === 'reject') {
                                const responseField = form.querySelector('textarea[name="response"]');
                                if (!responseField.value.trim()) {
                                    responseField.focus();
                                    responseField.classList.add('is-invalid');
                                    return false;
                                }
                            }
                            form.submit();
                        }
                    }
                });
            });
        });
    });
</script>
{% endblock %}