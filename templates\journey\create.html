{% block content %}
<div class="create-journey-modal" id="createJourneyModal">
  <form method="post" action="{{ url_for('journey.create_journey') }}" enctype="multipart/form-data"
    novalidate id="create-journey-form" class="needs-validation modern-form">

    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Grid Layout -->
      <div class="desktop-grid{% if not premium_access %} single-column{% endif %}">

        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-info-circle section-icon"></i>
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-journal-text"></i>
                  Journey Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title" value="{{ request.form.get('title', '') }}"
                  required minlength="5" maxlength="50" placeholder="Enter journey title" />
                <div class="invalid-feedback">Title is required and must be at least 5 characters long.</div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required
                  minlength="5" maxlength="250" rows="4" placeholder="Describe your journey...">{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">Description is required and must be at least 5 characters long.</div>
              </div>
            </div>
          </div>

          <!-- Journey Settings Section -->
          <div class="form-section compact">
            <div class="section-header">
              <i class="bi bi-gear section-icon"></i>
              <span class="section-title">Journey Settings</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="start_date" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date *
                </label>
                <input type="date" class="modern-input date-input" id="start_date" name="start_date"
                  value="{{ request.form.get('start_date', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              {% if user_blocked %}
              <div class="form-group">
                <div class="blocked-alert modern-alert">
                  <div class="alert-content">
                    <i class="bi bi-info-circle-fill alert-icon"></i>
                    <div class="alert-text">
                      <strong>Account Restricted</strong>
                      <p>You have been blocked from sharing journeys publicly, so your journey will remain private.</p>
                    </div>
                  </div>
                </div>
                <input type="hidden" name="visibility" value="private" />
              </div>
              {% else %}
              <div class="form-group">
                <label for="visibility" class="modern-label">
                  <i class="bi bi-eye"></i>
                  Visibility
                </label>
                <select id="visibility" name="visibility" class="modern-select">
                  <option value="private" selected>Private</option>
                  <option value="public">Public</option>
                  {% if premium_access %}
                  <option value="published">Published</option>
                  {% endif %}
                </select>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  <div class="visibility-help">
                    <strong>Private:</strong> Only visible to you<br>
                    <strong>Public:</strong> Visible to all users<br>
                    {% if premium_access %}
                    <strong>Published:</strong> Visible to everyone, including non-logged in users
                    {% endif %}
                  </div>
                </div>
              </div>
              {% endif %}

              {% if premium_access %}
              <div class="form-group">
                <div class="modern-checkbox">
                  <input type="checkbox" class="modern-checkbox-input" id="no_edits" name="no_edits">
                  <label class="modern-checkbox-label" for="no_edits">
                    <i class="bi bi-shield-lock"></i>
                    Prevent editors from editing this journey
                  </label>
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    When checked, editors and admins cannot edit your journey content, but they can still hide it if needed.
                  </div>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        {% if premium_access %}
        <!-- Right Column (Premium Only) -->
        <div class="right-column">
          <!-- Cover Image Section -->
          <div class="form-section image-section">
            <div class="section-header">
              <i class="bi bi-image section-icon"></i>
              <span class="section-title">Cover Image</span>
            </div>

            <div class="image-content">
              <!-- Image Preview Container -->
              <div id="image-preview-container" class="image-preview-area">
                <div class="placeholder-container">
                  <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                    alt="Journey placeholder" class="placeholder-image" id="current-image-preview">
                </div>
              </div>

              <!-- Image Upload -->
              <div class="form-group">
                <label for="image" class="modern-label">
                  <i class="bi bi-camera"></i>
                  Add Cover Image (optional)
                </label>
                <input type="file" class="modern-input" id="image" name="image"
                  accept="{{ file_config.allowedExtensionsHtml }}" data-premium="false" />
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{ file_config.allowedFormatsText }}.
                </div>
                <div class="invalid-feedback" id="imageFeedback">
                  Image cannot exceed {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{ file_config.allowedFormatsText }}.
                </div>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </form>

<!-- Modular CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/form-layouts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/location-search.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff-permissions.css') }}">

<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/image-preview.js') }}"></script>
<script src="{{ url_for('static', filename='js/file-validation.js') }}"></script>

<style>
/* Journey Create Specific Styles */
</style>

<script>
  // Initialize Journey Create Form
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('create-journey-form');
    if (!form) return;

    // Initialize enhanced form validation
    if (window.EnhancedFormValidation) {
      window.EnhancedFormValidation.initializeModernForm(form, {
        validateOnInput: true,
        validateOnBlur: true,
        showSuccessStates: true
      });
    }

    // Initialize image preview for cover image
    const imageInput = document.getElementById('image');
    if (imageInput && window.ImagePreview) {
      window.ImagePreview.initialize('#image', '#image-preview-container', {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        showFileName: true,
        showFileSize: true,
        onImageLoad: function(file, dataUrl) {
          // Update placeholder image
          const preview = document.getElementById('current-image-preview');
          if (preview) {
            preview.src = dataUrl;
          }
        },
        onError: function(message) {
          console.error('Image preview error:', message);
        }
      });
    }

    console.log('✅ Journey create form initialized');
  });
</script>
</div>
{% endblock %}