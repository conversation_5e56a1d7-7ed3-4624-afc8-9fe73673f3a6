{% extends "base.html" %}

{% block title %}
{{ journey.title }} - Footprints
{% endblock %}

{% block head %}
<!-- Modular CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/card-layouts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/comments.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/image-gallery.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/responsive.css') }}">

<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/journey-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/event-operations.js') }}"></script>
<script src="{{ url_for('static', filename='js/location-operations.js') }}"></script>
{% endblock %}

{% block content %}
<!-- Data attributes for JavaScript -->
<div id="pageData"
     data-journey-user-id="{{ journey.user_id }}"
     data-session-user-id="{{ session.user_id if session.user_id else '' }}"
     data-is-staff="{{ 'true' if can_manage_content() else 'false' }}"
     style="display: none;"></div>

<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back</span>
</a>
<div class="row g-4" id="journeyContainer">
  <!-- Journey details panel -->
  <div class="col-md-4">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h1 class="fs-3 fw-bold mb-0">Journey Details</h1>

          <div class="d-flex align-items-center gap-2">
            <!-- Follow button (visible for non-owners) -->
            {% if session.get('user_id') and journey.user_id != session.user_id %}
            <button type="button"
                    class="btn {% if is_following_journey %}btn-primary{% else %}{% endif %} btn-sm d-flex align-items-center px-3"
                    data-action="toggle-follow"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-heart{% if is_following_journey %}-fill{% endif %} me-1"></i>
              <span class="btn-text d-none d-sm-inline">{% if is_following_journey %}Following{% else %}Follow{% endif %}</span>
            </button>
            {% endif %}

            <!-- Journey menu -->
            {% if session.get('user_id') %}
            <div class="dropdown">
              <button class="menu-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                {% if journey.user_id != session.user_id %}
                <!-- Follow button moved out of dropdown -->
                {% endif %}
              {% if journey.user_id == session.user_id or can_manage_content() %}
              <li>
                {% if journey.no_edits and journey.user_id != session.user_id %}
                <a href="#" class="dropdown-item text-muted" onclick="showProtectedJourneyMessage()"
                  style="cursor: not-allowed;">
                  <i class="bi bi-shield-lock me-2"></i>Edit Journey (Protected)
                </a>
                {% else %}
                <button type="button" class="dropdown-item"
                        data-action="edit-journey"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-pencil me-2"></i>Edit Journey
                </button>
                {% endif %}
              </li>
              {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
              <li>
                <a href="{{ url_for('edit_history.get_journey_edit_history', journey_id=journey.id) }}"
                  class="dropdown-item">
                  <i class="bi bi-clock-history me-2"></i>View Edit History
                </a>
              </li>
              {% endif %}
              {% if can_manage_content() and not journey.user_id == session.user_id %}
              <li>
                <button type="button" class="dropdown-item text-warning"
                        data-action="toggle-hidden"
                        data-journey-id="{{ journey.id }}">
                  <i class="bi bi-slash-circle me-2"></i>
                  <span class="btn-text">{% if not journey.is_hidden %}Hide{% else %}Unhide{% endif %} Journey</span>
                </button>
              </li>
              {% endif %}
              {% if journey.user_id == session.user_id %}
              <li>
                <hr class="dropdown-divider">
              </li>
              <li>
                <form method="post"
                  action="{{ url_for('journey.delete_' + ('admin_' if is_admin_view else '') + 'journey', journey_id=journey.id) }}"
                  id="deleteJourneyForm" class="d-inline">
                  <button type="button" class="dropdown-item text-danger"
                          data-action="delete-journey"
                          data-journey-id="{{ journey.id }}">
                    <i class="bi bi-trash me-2"></i>Delete Journey
                  </button>
                </form>
              </li>
              {% endif %}
              {% endif %}
            </ul>
            </div>
            {% endif %}
          </div>
        </div>
        <div class="mb-2">
          <span
            class="badge visibility-badge {% if journey.visibility in ('public', 'published') %}bg-success{% else %}bg-secondary{% endif %} rounded-pill py-1 px-3">
            {% if journey.visibility in ('public', 'published') %}{{ journey.visibility|capitalize }}{% else
            %}Private{% endif %}
          </span>
          {% if journey.is_hidden %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 hidden-badge">Hidden</span>
          {% endif %}
          {% if journey.no_edits and (journey.user_id == session.user_id or can_manage_content()) %}
          <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1"
            title="This journey is protected from staff edits">
            <i class="bi bi-shield-lock me-1"></i>Protected
          </span>
          {% endif %}
        </div>

        <!-- Hidden Journey Appeal Section -->
        {% if journey.is_hidden and journey.user_id == session.user_id %}
        <div class="alert alert-warning rounded-3 mb-2" id="appealSection">
          <div class="d-flex align-items-start">
            <i class="bi bi-eye-slash fs-5 me-3 mt-1"></i>
            <div class="flex-grow-1">
              <h6 class="fw-bold mb-2">Journey Hidden by Staff</h6>
              <p class="mb-2 small">This journey has been hidden by content management staff and is not visible to other
                users.</p>

              <!-- Show rejection reason if appeal was rejected -->
              {% if appeal_status and appeal_status.status == 'rejected' and appeal_status.admin_response %}
              <div class="bg-light rounded p-3 mb-3" id="rejectionReason">
                <h6 class="small fw-bold mb-1 text-danger">
                  <i class="bi bi-x-circle me-1"></i>Appeal Rejected - Staff Response:
                </h6>
                <p class="mb-0 small">{{ appeal_status.admin_response }}</p>
              </div>
              {% endif %}

              <a href="{{ url_for('helpdesk.journey_appeal', journey_id=journey.id) }}"
                class="btn btn-sm btn-warning rounded-pill">
                <i class="bi bi-flag me-1"></i>
                {% if not appeal_status %}
                Submit Appeal
                {% elif appeal_status.status == 'rejected' %}
                Submit New Appeal
                {% elif appeal_status.status in ['new', 'open'] %}
                View Appeal Status
                {% else %}
                Appeal Decision
                {% endif %}
              </a>
            </div>
          </div>
        </div>
        {% endif %}

        <!-- Author info -->
        <div class="d-flex align-items-center mb-4">
          <div class="position-relative me-3">
            <div class="rounded-circle overflow-hidden">
              <img {% if journey.profile_image %}
                src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;"
                onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
              {% else %}
              <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                alt="{{ journey.username }}" style="width: 50px; height: 50px; object-fit: cover;">
              {% endif %}
            </div>
          </div>
        
          <div class="d-flex flex-column align-items-start">
            {% if journey.user_id != session.user_id %}
            <a href="{{ url_for('account.get_public_profile', username=journey.username) }}" 
               class="text-decoration-none username-link text-primary fw-semibold ">
              {{ journey.username }}
            </a>
            {% else %}
            <div class="fw-semibold">{{ journey.username }}</div>
            {% endif %}
            <small class="text-muted">Created on {{ journey.start_date.strftime('%B %d, %Y') }}</small>
          </div>
        </div>
        

        <!-- Journey info -->
        <div class="journey-info">
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Title</h6>
            <p class="text-dark fs-4 fw-medium mb-0" id="journeyTitle">{{ journey.title }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Start Date</h6>
            <p class="mb-0" id="journeyStartDate">{{ journey.start_date.strftime('%B %d, %Y') }}</p>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
            <p class="mb-0" id="journeyDescription">{{ journey.description }}</p>
          </div>
          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Cover Image</h6>
            <div class="cover-image-container mb-2">
              {% if journey.cover_image %}
              <div class="cover-image position-relative" id="coverImageContainer">
                <div class="cover-image-clickable"
                  data-image-url="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                  data-title="{{ journey.title }}"
                  onclick="showCoverImageModal(this.dataset.imageUrl, this.dataset.title)">
                  <img src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                    alt="Cover image" class="cover-image-img" id="coverImageImg">
                  <div class="cover-image-overlay">
                    <i class="bi bi-zoom-in"></i>
                    <span>Click to enlarge</span>
                  </div>
                </div>
                {% if journey.user_id == session.user_id or can_manage_content() %}
                <div class="cover-image-controls position-absolute bottom-0 end-0 m-2">
                  {% if journey.user_id == session.user_id %}
                  <button class="btn btn-sm btn-light rounded-pill me-2" id="changeCoverImageBtn">
                    <i class="bi bi-image me-1"></i> Change
                  </button>
                  {% endif %}
                  <button class="btn btn-sm btn-light rounded-pill" id="removeCoverImageBtn">
                    <i class="bi bi-trash me-1"></i> Remove
                  </button>
                </div>
                {% endif %}
              </div>
              {% else %}
              <div class="cover-image cover-image-placeholder d-flex align-items-center justify-content-center"
                id="coverImagePlaceholder">
                <div class="text-center">
                  {% if journey.user_id == session.user_id %}
                  {% if premium_access %}
                  <button class="btn btn-light btn-sm rounded-pill" id="addCoverImageBtn">
                    <i class="bi bi-image me-2"></i> Add Cover Image
                  </button>
                  {% else %}
                  <div class="text-muted small mb-2">Cover images are available for premium users</div>
                  <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
                    class="btn btn-primary btn-sm rounded-pill">
                    <i class="bi bi-star me-1"></i> Upgrade to Premium
                  </a>
                  {% endif %}
                  {% else %}
                  <div class="text-muted">No cover image</div>
                  {% endif %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Journey Map -->
          {% if locations and locations|length > 0 %}
          <div class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h6 class="text-uppercase text-muted small fw-bold mb-0">Journey Map</h6>
              <a href="{{url_for('location.get_journey_map', journey_id=journey.id)}}"
                class="btn btn-sm btn-outline-primary" target="_blank">
                <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
              </a>
            </div>
            <div class="journey-map-container">
              <div id="journeyMap" style="height: 250px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
            </div>
          </div>
          {% endif %}
        </div>



      </div>
    </div>
  </div>

  <!-- Events panel -->
  <div class="col-md-8">
    <div class="card shadow-sm border-0 rounded-3 h-100">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fs-3 fw-bold mb-0">Events <span class="badge bg-dark rounded-pill" id="eventCount">{{
              events|length }}</span></h2>
          {% if session.get('user_id') and journey.user_id == session.user_id and events %}
          <button class="btn btn-dark rounded-pill px-4 py-2"
                  data-action="create-event"
                  data-journey-id="{{ journey.id }}">
            <i class="bi bi-plus-lg me-2"></i> Add event
          </button>
          {% endif %}
        </div>

        {% if events %}
        <div class="event-content">
          <div class="timeline-container overflow-auto">
            <div class="timeline">
              {% set ns = namespace(displayed_dates={}) %}
              {% for event in events %}
              {% set current_date = event.start_datetime.strftime('%Y-%m-%d') %}

              <div class="timeline-item">
                {% if current_date not in ns.displayed_dates %}
                <div class="timeline-date-marker">
                  <div class="timeline-date">
                    <span class="badge bg-dark rounded-pill py-1 px-3">{{ event.start_datetime.strftime('%b %d, %Y')
                      }}</span>
                  </div>
                </div>
                {% set _ = ns.displayed_dates.update({current_date: true}) %}
                {% endif %}

                <div class="timeline-content-wrapper event-card" data-event-id="{{ event.id }}">
                  <div class="timeline-content card border-0 shadow-sm">
                    <div class="card-body p-0">
                      <!-- Main event content -->
                      <div class="d-flex event-main-content"
                        data-event-url="{{url_for('event.get_event_details', event_id=event.id)}}"
                        onclick="window.location.href=this.dataset.eventUrl"
                        style="cursor: pointer;">
                        {% if event.image %}
                        <div class="timeline-image-container">
                          <img src="{{ url_for('static', filename=get_safe_image_url(event.image, 'event')) }}"
                            class="timeline-image rounded-start" alt="{{ event.title }}">
                        </div>
                        {% else %}
                        <div class="timeline-image-container">
                          <img
                            src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                            class="timeline-image rounded-start" alt="Event placeholder" />
                        </div>
                        {% endif %}

                        <div class="p-3 w-100 position-relative">
                          <h5 class="mb-2 fw-semibold text-truncate">{{ event.title }}</h5>
                          <p class="mb-2 text-muted small"
                            style="min-height: 40px; max-height: 60px; overflow: hidden;">{{ event.description }}
                          </p>

                          <div class="d-flex justify-content-between align-items-center">
                            {% if session.get('user_id') %}
                            <div class="d-flex align-items-center text-primary small event-location-button"
                              style="cursor: pointer;" data-event-id="{{ event.id }}"
                              data-latitude="{{ event.latitude }}" data-longitude="{{ event.longitude }}"
                              data-location-name="{{ event.location_name }}"
                              onclick="event.stopPropagation(); toggleEventMap(this.dataset.eventId, this.dataset.latitude || null, this.dataset.longitude || null, this.dataset.locationName)">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                              <i class="bi bi-chevron-down ms-2 map-toggle-icon" id="mapToggle{{ event.id }}"></i>
                            </div>
                            {% else %}
                            <!-- Non-logged-in users see location but cannot interact -->
                            <div class="d-flex align-items-center text-muted small">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                            </div>
                            {% endif %}
                            <small class="text-muted">{{ event.start_datetime.strftime('%I:%M %p') }}</small>
                          </div>
                        </div>
                      </div>

                      <!-- Collapsible map section -->
                      <div class="event-map-container" id="eventMap{{ event.id }}" style="display: none;">
                        <div class="border-top">
                          <div class="p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                              <h6 class="mb-0 text-muted">Location</h6>
                              {% if session.get('user_id') %}
                              <button class="btn btn-sm btn-outline-primary"
                                data-latitude="{{ event.latitude }}" data-longitude="{{ event.longitude }}"
                                data-location-name="{{ event.location_name }}"
                                onclick="openFullMap(this.dataset.latitude || null, this.dataset.longitude || null, this.dataset.locationName)">
                                <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
                              </button>
                              {% endif %}
                            </div>
                            <div id="inlineMap{{ event.id }}"
                              style="height: 200px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {% endfor %}
            </div>
          </div>
        </div>
        {% else %}
        <div class="event-content d-flex flex-column justify-content-center align-items-center">
          <div class="empty-state text-center">
            <div class="empty-state-icon mb-4">
              <i class="bi bi-calendar-x" style="font-size: 5rem; color: #d1d1d1;"></i>
            </div>
            <h3 class="mb-3 fw-bold" id="noEventsMessage">No events yet</h3>
            <p class="text-muted mb-4">{% if journey.user_id == session.user_id %}Start your journey by adding your
              first
              event!{% else %}This journey doesn't have any events yet.{% endif %}</p>
            {% if session.get('user_id') and journey.user_id == session.user_id %}
            <button class="btn btn-primary rounded-pill px-4 py-2"
                    data-action="create-event"
                    data-journey-id="{{ journey.id }}">
              <i class="bi bi-plus-lg me-2"></i> Add your first event
            </button>
            {% endif %}
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Hidden file input for cover image uploads -->
<input type="file" id="coverImageInput" accept=".png,.jpg,.jpeg,.gif" style="display: none;">

<style>
.username-link {
  transition: all 0.2s ease;
  padding: 2px 6px;
  border-radius: 4px;
  margin: -2px -6px; 
  display: inline; 
}

.username-link:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}



  .rounded-circle,
  .rounded-circle img {
    position: relative;
    z-index: 1;
  }

  .map-preview-container {
    position: relative;
    display: inline-block;
    z-index: 2;
  }


  .view-map-link {
    text-decoration: none;
    color: black;
  }

  .view-map-link:hover {
    text-decoration: none;
    color: grey;
  }


  .map-preview {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    height: 200px;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 1080;
    overflow: hidden;
    text-align: center;
  }

  .map-preview.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }

  .map-preview::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #c8e6c9 0%, #fff59d 50%);
    border-left: 2px solid #e0e0e0;
    border-top: 2px solid #e0e0e0;
    transform: rotate(45deg);
    z-index: 1080;
  }

  /* Journey Map Styling */
  .journey-map-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  #journeyMap {
    border-radius: 8px;
  }

  .leaflet-popup-content {
    margin: 8px 12px;
  }

  .event-popup h6 {
    color: #495057;
    margin-bottom: 8px;
  }

  .location-popup h6 {
    color: #495057;
    margin-bottom: 8px;
  }

  /* Menu button styling - matching event detail design */
  .menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    color: #495057;
    transition: all 0.2s;
  }

  .menu-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
  }

  .menu-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
  }

  /* Follow button styling - matching event detail theme */
  .btn[data-action="toggle-follow"] {
    transition: all 0.2s ease;
    border-width: 1px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 16px;
  }

  .btn[data-action="toggle-follow"]:not(.btn-primary) {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
  }

  .btn[data-action="toggle-follow"]:not(.btn-primary):hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .btn[data-action="toggle-follow"].btn-primary {
    background: #007bff;
    border-color: #007bff;
    color: white;
  }

  .btn[data-action="toggle-follow"].btn-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
  }

  .btn[data-action="toggle-follow"]:active {
    transform: translateY(0);
  }

  .btn[data-action="toggle-follow"] i.bi-heart-fill {
    color: #dc3545;
  }

  /* Button group spacing - modern approach */
  .d-flex.gap-2 {
    gap: 8px;
  }

  /* Responsive follow button text */
  @media (max-width: 575.98px) {
    .btn[data-action="toggle-follow"] .btn-text {
      display: none !important;
    }

    .btn[data-action="toggle-follow"] {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }
  }

  /* Event map styling */
  .event-map-container {
    transition: all 0.3s ease;
  }

  .event-location-button {
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 6px;
  }

  .event-location-button:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateY(-1px);
  }

  .map-toggle-icon {
    transition: transform 0.2s ease;
  }

  .event-main-content {
    transition: background-color 0.2s ease;
  }

  .event-main-content:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }

  .event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .event-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .cover-image-img {
    width: 80%;
    height: 100px;
    border-radius: 8px;
    display: block;
  }

  #journeyContainer {
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: row;
  }

  #journeyContainer .col-md-4,
  #journeyContainer .col-md-8 {
    display: flex;
    flex-direction: column;
  }

  #journeyContainer .card {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  #journeyContainer .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .event-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .event-content.d-flex.flex-column.justify-content-center.align-items-center {
    height: 100%;
    flex: 1;
  }

  .col-md-4 .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
  }

  .journey-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .journey-info .mb-4:nth-child(1),
  .journey-info .mb-4:nth-child(2) {
    flex-shrink: 0;
  }

  .journey-info .mb-4:nth-child(3) {
    min-height: 0;
    overflow-y: auto;
    padding-right: 5px;
  }

  /* Scrollbar styling */
  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar {
    width: 6px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
  }

  .journey-info .mb-4:nth-child(3)::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* For Firefox */
  .journey-info .mb-4:nth-child(3) {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  .timeline-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0; /* Allow flex item to shrink below content size */
  }

  .timeline {
    position: relative;
    padding-left: 2rem;
  }

  .timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0.35rem;
    width: 2px;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
    height: 100%;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
  }

  .timeline-date-marker {
    position: relative;
    margin-bottom: 1rem;
    margin-left: -0.5rem;
  }

  .timeline-date {
    margin-bottom: 0.5rem;
  }

  .timeline-content {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 0.5rem;
    width: 100%;
  }

  .timeline-content-wrapper {
    position: relative;
    overflow: visible;
  }


  .timeline-content.card {
    border: none !important;
    overflow: hidden;
  }

  .event-card-link {
    display: block;
    z-index: 1;
  }

  .timeline-image-container {
    width: 120px;
    height: 120px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    overflow: hidden;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }


  .timeline-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
    display: block;
  }

  .dropdown-menu {
    z-index: 1060 !important;
    overflow: visible;
    min-width: 10rem;
    background-color: #ffffff !important;
    border: 1px solid rgba(0, 0, 0, .15) !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, .15) !important;
  }

  .dropdown-item {
    z-index: 1060 !important;
    position: relative;
  }

  .card {
    border-radius: 0.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .journey-info .text-uppercase {
    letter-spacing: 0.5px;
    font-size: 0.7rem;
  }

  .empty-state {
    max-width: 400px;
    padding: 2rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .empty-state-icon {
    margin: 0 auto;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  @media (max-width: 767.98px) {
    .timeline {
      padding-left: 1.5rem;
    }

    .timeline-image-container {
      width: 80px;
      height: 80px;
    }

    .timeline-image {
      width: 80px;
      height: 80px;
    }

    .col-md-4 .card-body {
      max-height: none;
    }

    .journey-info .mb-4:nth-child(3) {
      max-height: 150px;
    }

    .timeline-content-wrapper .event-card-link>div {
      border: none !important;
      box-shadow: none !important;
    }

    .timeline-content.card,
    .timeline-content .card-body {
      border: none !important;
      box-shadow: none !important;
    }
  }

  /* Cover image styling */
  .cover-image-container {
    margin-top: 15px;
  }

  .cover-image {
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    height: 200px;
    /* Fixed height for consistent display */
  }

  .cover-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .cover-image-placeholder {
    height: 200px;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
  }

  .cover-image-controls {
    z-index: 5;
  }

  .cover-image:hover .cover-image-controls {
    opacity: 1;
  }

  /* Clickable cover image styling */
  .cover-image-clickable {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .cover-image-clickable:hover {
    transform: scale(1.02);
  }

  .cover-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 8px;
  }

  .cover-image-clickable:hover .cover-image-overlay {
    opacity: 1;
  }

  .cover-image-overlay i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .cover-image-overlay span {
    font-size: 0.875rem;
  }

  /* Simple Image modal styling */
  .image-modal .modal-dialog {
    max-width: 90vw;
    max-height: 90vh;
  }

  .image-modal .modal-content {
    background: transparent;
    border: none;
    box-shadow: none;
  }

  .image-modal .modal-header {
    border: none;
    padding: 1rem;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1050;
    background: none;
  }

  .image-modal .modal-title {
    display: none;
  }

  .image-modal .modal-body {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
  }

  .image-modal .modal-footer {
    display: none;
  }

  .image-modal img {
    max-width: 100%;
    max-height: 85vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .image-modal .modal-dialog {
      margin: 1rem;
      max-width: calc(100vw - 2rem);
    }

    .image-modal img {
      max-height: 80vh;
    }
  }

  /* Enhanced cover image controls */
  .cover-image-controls {
    opacity: 0;
    transition: opacity 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 4px;
  }

  .cover-image-controls .btn {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
  }

  .cover-image-controls .btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  /* Loading state for cover image upload */
  .cover-image-uploading {
    position: relative;
    overflow: hidden;
  }

  .cover-image-uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .cover-image-uploading::before {
    content: 'Uploading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    z-index: 11;
  }
</style>
<script>
  // Global variables from server data attributes
  const pageData = document.getElementById('pageData');
  const JOURNEY_USER_ID = parseInt(pageData.dataset.journeyUserId);
  const SESSION_USER_ID = pageData.dataset.sessionUserId ? parseInt(pageData.dataset.sessionUserId) : null;
  const IS_STAFF = pageData.dataset.isStaff === 'true';

  // Simple and reliable back button function
  function smartBack() {
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      try {
        const decodedUrl = decodeURIComponent(backUrl);
        if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
          window.location.href = decodedUrl;
          return;
        }
      } catch (e) {
        // Invalid URL, continue to next method
      }
    }

    if (window.history.length > 1) {
      window.history.back();
      return;
    }

    window.location.href = '{{ url_for("journey.get_public_journeys") }}';
  }

  // Simple back button text update
  function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');

    if (backUrl) {
      if (backUrl.includes('/journey/public') || backUrl.includes('/discovery')) {
        backButtonText.textContent = 'Back to Discovery';
      } else if (backUrl.includes('/journey/private')) {
        backButtonText.textContent = 'Back to My Journeys';
      } else if (backUrl.includes('/journey/manage')) {
        backButtonText.textContent = 'Back to Management';
      } else if (backUrl.includes('/published_journey')) {
        backButtonText.textContent = 'Back to Published Journeys';
      } else {
        backButtonText.textContent = 'Back';
      }
      return;
    }

    const referrer = document.referrer;
    if (referrer && referrer.includes('/journey/public')) {
      backButtonText.textContent = 'Back to Discovery';
    } else if (referrer && referrer.includes('/journey/private')) {
      backButtonText.textContent = 'Back to My Journeys';
    } else if (referrer && referrer.includes('/journey/manage')) {
      backButtonText.textContent = 'Back to Management';
    } else {
      backButtonText.textContent = 'Back';
    }
  }

  document.addEventListener('DOMContentLoaded', function () {
    updateBackButtonText();
  });

  // Map initialization - now handled by modular utilities

  // Timeline adjustment utility
  function adjustTimelineLine() {
    const timeline = document.querySelector('.timeline');
    const container = document.querySelector('.timeline-container');
    if (timeline && container) {
      timeline.style.minHeight = '';
    }
  }
  window.addEventListener('load', adjustTimelineLine);
  window.addEventListener('resize', adjustTimelineLine);
  adjustTimelineLine();

  // Legacy event handling removed - now handled by modular utilities

  // Legacy create event handling removed - now handled by modular utilities

  // All remaining legacy JavaScript removed - functionality moved to modular utilities
</script>

{% endblock %}
  // All JavaScript functionality moved to modular utilities

