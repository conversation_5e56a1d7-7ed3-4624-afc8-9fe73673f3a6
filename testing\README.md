# 🐍 Python Testing Suite for COMP639 Project

This directory contains all Python-based testing code, configurations, and utilities for the COMP639 webapp project.

**Language**: Pure Python (no JavaScript dependencies)
**Database**: MySQL with `mysql-connector-python` (matching your project's database setup)
**Browser Testing**: Python Playwright or Selenium (your choice)

## 📁 Directory Structure

```
testing/
├── README.md                          # This file
├── requirements.txt                   # Python testing dependencies
├── pytest.ini                        # Pytest configuration
│
├── scripts/                           # Python testing automation scripts
│   ├── test_manager.py               # Test management utilities
│   ├── browser_recorder.py          # Browser test recorder (Python)
│   ├── setup_testing.py             # Testing environment setup
│   └── analyze_functions.py         # Template function analyzer
│
├── browser_tests/                     # Python browser tests
│   ├── conftest.py                   # Browser test configuration
│   ├── auth/                         # Authentication tests
│   ├── journeys/                     # Journey-related tests
│   ├── events/                       # Event-related tests
│   ├── admin/                        # Admin functionality tests
│   └── utils/                        # Browser testing utilities
│
├── unit_tests/                        # Python unit tests
│   ├── conftest.py                   # Unit test configuration
│   ├── data/                         # Data layer tests
│   ├── services/                     # Service layer tests
│   ├── routes/                       # Route tests
│   └── utils/                        # Utility tests
│
├── integration_tests/                 # Python integration tests
│   ├── conftest.py                   # Integration test configuration
│   ├── api/                          # API endpoint tests
│   ├── database/                     # Database integration tests
│   └── workflows/                    # Complete workflow tests
│
├── reports/                           # Test reports and artifacts
│   ├── coverage/                     # Code coverage reports
│   ├── html/                         # HTML test reports
│   ├── screenshots/                  # Test screenshots
│   └── videos/                       # Test videos (if enabled)
│
├── fixtures/                          # Test data and fixtures
│   ├── test_users.json              # Test user data
│   ├── test_journeys.json           # Test journey data
│   └── mock_responses.json          # Mock API responses
│
└── docs/                             # Testing documentation
    ├── PYTHON_TESTING_GUIDE.md      # Comprehensive Python testing guide
    ├── BROWSER_TESTING_GUIDE.md     # Browser testing with Python
    └── SETUP_GUIDE.md               # Setup and configuration guide
```

## 🚀 Quick Start

### 1. Setup Testing Environment

```bash
cd testing
python scripts/setup_testing.py
```

### 2. Install Dependencies

```bash
# Python dependencies
pip install -r requirements.txt

# Node.js dependencies
npm install

# Playwright browsers
npx playwright install
```

### 3. Record Your First Test

```bash
python scripts/record_interactions.py
```

### 4. Run All Tests

```bash
# Unit tests
pytest unit/

# Playwright tests
npm test

# All tests
python scripts/test_manager.py run-all
```

## 📊 Test Types

### 🎭 Playwright E2E Tests

- **Location**: `playwright/tests/`
- **Purpose**: End-to-end user interaction testing
- **Tools**: Playwright, Browser automation
- **Coverage**: Full user workflows

### 🔬 Unit Tests

- **Location**: `unit/`
- **Purpose**: Individual component testing
- **Tools**: Pytest, Mock objects
- **Coverage**: Services, data layers, utilities

### 📈 Function Analysis

- **Location**: `scripts/analyze_functions.py`
- **Purpose**: Identify unused JavaScript functions
- **Tools**: Static analysis, Runtime tracking
- **Coverage**: Template optimization

## 🛠️ Key Features

### Interactive Test Recording

- Record browser interactions automatically
- Convert recordings to maintainable test code
- Support for authenticated and non-authenticated flows

### Comprehensive Reporting

- HTML reports with screenshots and videos
- Code coverage analysis
- Function usage tracking
- Performance metrics

### CI/CD Ready

- Automated test execution
- Multiple browser support
- Parallel test execution
- Artifact collection

## 📚 Documentation

- **[Testing Guide](docs/TESTING_GUIDE.md)** - Complete testing documentation
- **[Playwright Guide](docs/PLAYWRIGHT_GUIDE.md)** - E2E testing with Playwright
- **[Unit Testing Guide](docs/UNIT_TESTING_GUIDE.md)** - Python unit testing
- **[CI/CD Guide](docs/CI_CD_GUIDE.md)** - Continuous integration setup

## 🔧 Configuration

All testing configurations are centralized in this directory:

- `playwright.config.js` - Playwright settings
- `conftest.py` - Pytest configuration
- `package.json` - Node.js dependencies and scripts

## 🎯 Best Practices

1. **Separate Concerns**: Keep testing code separate from application code
2. **Realistic Tests**: Record actual user workflows
3. **Maintainable Code**: Use page objects and utilities
4. **Comprehensive Coverage**: Test happy paths and edge cases
5. **Regular Cleanup**: Remove unused test code and functions

## 🆘 Getting Help

1. Check the documentation in `docs/`
2. Run `python scripts/test_manager.py --help`
3. Use debug mode: `npm run test:debug`
4. Review test artifacts in `reports/`

---

**Note**: This testing suite is completely separate from the main webapp code, ensuring clean separation of concerns and easier maintenance.
