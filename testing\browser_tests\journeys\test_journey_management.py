"""
Browser tests for journey management

Tests complete journey management workflows using browser automation:
- Journey creation and editing
- Journey visibility settings
- Journey deletion
- Journey listing and filtering
"""

import pytest
import time

@pytest.mark.browser
@pytest.mark.journey
class TestJourneyCreationBrowser:
    """Test journey creation using browser automation"""
    
    def test_create_journey_playwright(self, authenticated_playwright_page, base_url):
        """Test journey creation workflow with <PERSON><PERSON>"""
        page = authenticated_playwright_page
        
        # Navigate to journey creation page
        page.goto(f"{base_url}/journey/private/new")
        
        # Wait for form to load
        page.wait_for_selector('input[name="title"]')
        
        # Fill journey form
        page.fill('input[name="title"]', 'Browser Test Journey')
        page.fill('textarea[name="description"]', 'Created using browser automation')
        page.fill('input[name="start_date"]', '2025-01-01')
        
        # Select visibility
        page.select_option('select[name="visibility"]', 'private')
        
        # Submit form
        page.click('button[type="submit"]')
        
        # Wait for redirect or success message
        page.wait_for_timeout(2000)
        
        # Verify journey was created
        assert "journey" in page.url.lower() or page.locator('text=success').is_visible()
    
    def test_create_journey_selenium(self, authenticated_selenium_driver, base_url):
        """Test journey creation workflow with Selenium"""
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.support.ui import Select
        
        driver = authenticated_selenium_driver
        
        # Navigate to journey creation page
        driver.get(f"{base_url}/journey/private/new")
        
        # Wait for form elements
        wait = WebDriverWait(driver, 10)
        title_field = wait.until(EC.presence_of_element_located((By.NAME, "title")))
        
        # Fill journey form
        title_field.send_keys('Selenium Test Journey')
        
        description_field = driver.find_element(By.NAME, "description")
        description_field.send_keys('Created using Selenium automation')
        
        date_field = driver.find_element(By.NAME, "start_date")
        date_field.send_keys('2025-01-01')
        
        # Select visibility
        visibility_select = Select(driver.find_element(By.NAME, "visibility"))
        visibility_select.select_by_value('private')
        
        # Submit form
        submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
        submit_button.click()
        
        # Wait for redirect
        wait.until(lambda d: "journey" in d.current_url.lower() or 
                   d.find_elements(By.XPATH, "//*[contains(text(), 'success')]"))
        
        # Verify journey was created
        assert "journey" in driver.current_url.lower()


@pytest.mark.browser
@pytest.mark.journey
class TestJourneyEditingBrowser:
    """Test journey editing using browser automation"""
    
    def test_edit_journey_playwright(self, authenticated_playwright_page, base_url):
        """Test journey editing workflow with Playwright"""
        page = authenticated_playwright_page
        
        # First create a journey to edit
        page.goto(f"{base_url}/journey/private/new")
        page.wait_for_selector('input[name="title"]')
        page.fill('input[name="title"]', 'Journey to Edit')
        page.fill('textarea[name="description"]', 'Original description')
        page.fill('input[name="start_date"]', '2025-01-01')
        page.click('button[type="submit"]')
        page.wait_for_timeout(2000)
        
        # Navigate to journey list to find edit link
        page.goto(f"{base_url}/journey/private")
        
        # Look for edit button/link (adjust selector based on your UI)
        if page.locator('a[href*="edit"]').count() > 0:
            page.click('a[href*="edit"]')
            
            # Wait for edit form
            page.wait_for_selector('input[name="title"]')
            
            # Verify form is pre-filled
            title_value = page.input_value('input[name="title"]')
            assert 'Journey to Edit' in title_value
            
            # Edit the journey
            page.fill('input[name="title"]', 'Edited Journey Title')
            page.fill('textarea[name="description"]', 'Updated description')
            page.select_option('select[name="visibility"]', 'public')
            
            # Submit changes
            page.click('button[type="submit"]')
            page.wait_for_timeout(2000)
            
            # Verify changes were saved
            assert page.locator('text=success').is_visible() or "journey" in page.url
    
    def test_journey_visibility_change(self, authenticated_playwright_page, base_url):
        """Test changing journey visibility"""
        page = authenticated_playwright_page
        
        # Create private journey
        page.goto(f"{base_url}/journey/private/new")
        page.wait_for_selector('input[name="title"]')
        page.fill('input[name="title"]', 'Visibility Test Journey')
        page.fill('textarea[name="description"]', 'Testing visibility changes')
        page.fill('input[name="start_date"]', '2025-01-01')
        page.select_option('select[name="visibility"]', 'private')
        page.click('button[type="submit"]')
        page.wait_for_timeout(2000)
        
        # Check that journey appears in private list
        page.goto(f"{base_url}/journey/private")
        assert page.locator('text=Visibility Test Journey').is_visible()
        
        # Edit to make public
        if page.locator('a[href*="edit"]').count() > 0:
            page.click('a[href*="edit"]')
            page.wait_for_selector('select[name="visibility"]')
            page.select_option('select[name="visibility"]', 'public')
            page.click('button[type="submit"]')
            page.wait_for_timeout(2000)
            
            # Check that journey now appears in public list
            page.goto(f"{base_url}/journey/public")
            assert page.locator('text=Visibility Test Journey').is_visible()


@pytest.mark.browser
@pytest.mark.journey
class TestJourneyListingBrowser:
    """Test journey listing and filtering"""
    
    def test_journey_list_navigation(self, authenticated_playwright_page, base_url):
        """Test navigation between journey lists"""
        page = authenticated_playwright_page
        
        # Test private journeys list
        page.goto(f"{base_url}/journey/private")
        assert page.locator('h1, h2').filter(has_text='journey').count() > 0
        
        # Test public journeys list
        page.goto(f"{base_url}/journey/public")
        assert page.locator('h1, h2').filter(has_text='journey').count() > 0
        
        # Test navigation links
        if page.locator('a[href*="private"]').count() > 0:
            page.click('a[href*="private"]')
            assert "private" in page.url
        
        if page.locator('a[href*="public"]').count() > 0:
            page.click('a[href*="public"]')
            assert "public" in page.url
    
    def test_journey_search_functionality(self, authenticated_playwright_page, base_url):
        """Test journey search if available"""
        page = authenticated_playwright_page
        
        # Navigate to journey list
        page.goto(f"{base_url}/journey/private")
        
        # Look for search functionality
        if page.locator('input[type="search"], input[name*="search"]').count() > 0:
            search_input = page.locator('input[type="search"], input[name*="search"]').first
            search_input.fill('test')
            
            # Submit search (look for search button or form)
            if page.locator('button[type="submit"]').count() > 0:
                page.click('button[type="submit"]')
                page.wait_for_timeout(1000)
                
                # Verify search results
                assert page.url != f"{base_url}/journey/private"  # URL should change
    
    def test_journey_pagination(self, authenticated_playwright_page, base_url):
        """Test journey pagination if available"""
        page = authenticated_playwright_page
        
        # Navigate to journey list
        page.goto(f"{base_url}/journey/private")
        
        # Look for pagination controls
        if page.locator('a[href*="page"], .pagination').count() > 0:
            # Test pagination navigation
            if page.locator('a:has-text("Next"), a:has-text("2")').count() > 0:
                page.click('a:has-text("Next"), a:has-text("2")')
                page.wait_for_timeout(1000)
                
                # Verify page changed
                assert "page" in page.url or page.url != f"{base_url}/journey/private"


@pytest.mark.browser
@pytest.mark.journey
class TestJourneyDeletionBrowser:
    """Test journey deletion using browser automation"""
    
    def test_journey_deletion_confirmation(self, authenticated_playwright_page, base_url):
        """Test journey deletion with confirmation"""
        page = authenticated_playwright_page
        
        # Create journey to delete
        page.goto(f"{base_url}/journey/private/new")
        page.wait_for_selector('input[name="title"]')
        page.fill('input[name="title"]', 'Journey to Delete')
        page.fill('textarea[name="description"]', 'This will be deleted')
        page.fill('input[name="start_date"]', '2025-01-01')
        page.click('button[type="submit"]')
        page.wait_for_timeout(2000)
        
        # Navigate to journey list
        page.goto(f"{base_url}/journey/private")
        
        # Look for delete button/link
        if page.locator('button:has-text("Delete"), a:has-text("Delete")').count() > 0:
            # Handle confirmation dialog
            page.on('dialog', lambda dialog: dialog.accept())
            
            # Click delete
            page.click('button:has-text("Delete"), a:has-text("Delete")')
            page.wait_for_timeout(2000)
            
            # Verify journey was deleted
            assert not page.locator('text=Journey to Delete').is_visible()
    
    def test_journey_deletion_cancellation(self, authenticated_playwright_page, base_url):
        """Test cancelling journey deletion"""
        page = authenticated_playwright_page
        
        # Create journey
        page.goto(f"{base_url}/journey/private/new")
        page.wait_for_selector('input[name="title"]')
        page.fill('input[name="title"]', 'Journey Not to Delete')
        page.fill('textarea[name="description"]', 'This should remain')
        page.fill('input[name="start_date"]', '2025-01-01')
        page.click('button[type="submit"]')
        page.wait_for_timeout(2000)
        
        # Navigate to journey list
        page.goto(f"{base_url}/journey/private")
        
        # Look for delete button/link
        if page.locator('button:has-text("Delete"), a:has-text("Delete")').count() > 0:
            # Handle confirmation dialog - dismiss it
            page.on('dialog', lambda dialog: dialog.dismiss())
            
            # Click delete
            page.click('button:has-text("Delete"), a:has-text("Delete")')
            page.wait_for_timeout(1000)
            
            # Verify journey still exists
            assert page.locator('text=Journey Not to Delete').is_visible()


@pytest.mark.browser
@pytest.mark.journey
@pytest.mark.slow
class TestJourneyImageUpload:
    """Test journey image upload functionality"""
    
    def test_journey_cover_image_upload(self, authenticated_playwright_page, base_url, test_image_file):
        """Test uploading journey cover image"""
        page = authenticated_playwright_page
        
        # Navigate to journey creation
        page.goto(f"{base_url}/journey/private/new")
        page.wait_for_selector('input[name="title"]')
        
        # Fill basic journey info
        page.fill('input[name="title"]', 'Journey with Cover Image')
        page.fill('textarea[name="description"]', 'Testing image upload')
        page.fill('input[name="start_date"]', '2025-01-01')
        
        # Upload cover image if file input exists
        if page.locator('input[type="file"]').count() > 0:
            # Create a test image file
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
                tmp_file.write(test_image_file.getvalue())
                tmp_file_path = tmp_file.name
            
            try:
                page.set_input_files('input[type="file"]', tmp_file_path)
                
                # Submit form
                page.click('button[type="submit"]')
                page.wait_for_timeout(3000)  # Image upload might take longer
                
                # Verify success
                assert page.locator('text=success').is_visible() or "journey" in page.url
                
            finally:
                # Clean up temp file
                os.unlink(tmp_file_path)
