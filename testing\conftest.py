"""
Global test configuration for Footprints webapp Python testing

This module provides global pytest configuration and fixtures that are available
to all test modules across unit, browser, and integration tests.
"""

import sys
from pathlib import Path
import pytest
import os

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def pytest_configure(config):
    """Configure pytest with custom markers and settings"""
    config.addinivalue_line(
        "markers", "unit: Unit tests for individual components"
    )
    config.addinivalue_line(
        "markers", "browser: Browser automation tests"
    )
    config.addinivalue_line(
        "markers", "integration: Integration tests for component interaction"
    )
    config.addinivalue_line(
        "markers", "auth: Authentication and authorization tests"
    )
    config.addinivalue_line(
        "markers", "journey: Journey-related functionality tests"
    )
    config.addinivalue_line(
        "markers", "event: Event-related functionality tests"
    )
    config.addinivalue_line(
        "markers", "admin: Admin panel functionality tests"
    )
    config.addinivalue_line(
        "markers", "slow: Slow-running tests"
    )
    config.addinivalue_line(
        "markers", "database: Tests that interact with database"
    )
    config.addinivalue_line(
        "markers", "api: API endpoint tests"
    )

@pytest.fixture(scope="session")
def project_root():
    """Project root directory path"""
    return Path(__file__).parent.parent

@pytest.fixture(scope="session")
def testing_root():
    """Testing directory root path"""
    return Path(__file__).parent

@pytest.fixture(scope="session")
def test_config():
    """Global test configuration"""
    return {
        "base_url": os.getenv("TEST_BASE_URL", "http://127.0.0.1:5000"),
        "browser_type": os.getenv("BROWSER_TYPE", "chromium"),
        "headless": os.getenv("HEADLESS", "true").lower() == "true",
        "timeout": int(os.getenv("DEFAULT_TIMEOUT", "30")),
        "browser_timeout": int(os.getenv("BROWSER_TIMEOUT", "60")),
        "capture_screenshots": os.getenv("CAPTURE_SCREENSHOTS", "true").lower() == "true",
        "capture_videos": os.getenv("CAPTURE_VIDEOS", "false").lower() == "true",
    }

@pytest.fixture(scope="session")
def reports_dir(testing_root):
    """Reports directory for test artifacts"""
    reports_dir = testing_root / "reports"
    reports_dir.mkdir(exist_ok=True)
    return reports_dir

@pytest.fixture(scope="session")
def fixtures_dir(testing_root):
    """Fixtures directory for test data"""
    return testing_root / "fixtures"

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file paths"""
    for item in items:
        # Add markers based on file path
        if "unit_tests" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "browser_tests" in str(item.fspath):
            item.add_marker(pytest.mark.browser)
        elif "integration_tests" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add feature-specific markers
        if "auth" in str(item.fspath):
            item.add_marker(pytest.mark.auth)
        elif "journey" in str(item.fspath):
            item.add_marker(pytest.mark.journey)
        elif "event" in str(item.fspath):
            item.add_marker(pytest.mark.event)
        elif "admin" in str(item.fspath):
            item.add_marker(pytest.mark.admin)

def pytest_html_report_title(report):
    """Customize HTML report title"""
    report.title = "Footprints Webapp Test Report"

def pytest_html_results_summary(prefix, summary, postfix):
    """Customize HTML report summary"""
    prefix.extend([
        "<h2>Footprints Webapp Testing Suite</h2>",
        "<p>Comprehensive Python testing for Flask webapp</p>"
    ])

def pytest_runtest_makereport(item, call):
    """Customize test reporting"""
    if "incremental" in item.keywords:
        if call.excinfo is not None:
            parent = item.parent
            parent._previousfailed = item

def pytest_runtest_setup(item):
    """Setup for incremental tests"""
    if "incremental" in item.keywords:
        previousfailed = getattr(item.parent, "_previousfailed", None)
        if previousfailed is not None:
            pytest.xfail("previous test failed (%s)" % previousfailed.name)
