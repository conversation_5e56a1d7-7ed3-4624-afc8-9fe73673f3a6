# 🐍 Python Testing Guide for COMP639 Footprints Webapp

This guide covers comprehensive Python-based testing for your **Footprints** Flask webapp using pytest, Playwright, and Selenium.

## 🎯 Overview

This testing suite is **100% Python** - no JavaScript required! It provides:

- **🔬 Unit Testing** with pytest and mocking for services, routes, and data layers
- **🌐 Browser Testing** with Python Playwright or Selenium for E2E workflows
- **🔗 Integration Testing** for complete user journeys and API endpoints
- **📊 Coverage Reporting** with detailed HTML reports and metrics
- **⚡ Parallel Execution** for faster test runs across multiple workers
- **🗄️ MySQL Integration** matching your production database setup

## 🚀 Quick Start

### 1. Setup Testing Environment

```bash
cd testing
python scripts/setup_testing.py
```

### 2. Run Your First Tests

```bash
# Interactive menu
python scripts/test_manager.py menu

# Or run specific test types
python scripts/test_manager.py unit
python scripts/test_manager.py browser
python scripts/test_manager.py all
```

### 3. View Results

```bash
# Generate coverage report
python scripts/test_manager.py coverage

# Generate HTML report
python scripts/test_manager.py html-report
```

## 📁 Directory Structure

```
testing/
├── 🐍 Pure Python Testing Suite
├── unit_tests/           # Unit tests for services, routes, utils
├── browser_tests/        # Browser automation tests
├── integration_tests/    # End-to-end workflow tests
├── fixtures/             # Test data and fixtures
├── reports/              # Test reports and coverage
└── scripts/              # Test management utilities
```

## 🔬 Unit Testing

### Running Unit Tests

```bash
# All unit tests
python scripts/test_manager.py unit

# Specific category
python scripts/test_manager.py unit --category services
python scripts/test_manager.py unit --category routes

# With coverage
python scripts/test_manager.py coverage
```

### Writing Unit Tests

```python
# unit_tests/services/test_journey_service.py
import pytest

@pytest.mark.unit
@pytest.mark.journey
def test_create_journey(mock_execute_query, sample_user):
    """Test journey creation"""
    mock_execute_query.return_value = 123

    from services.journey_service import create_journey

    success, message, journey_id = create_journey(
        user_id=sample_user['id'],
        title="Test Journey",
        description="Test Description",
        start_date="2025-01-01",
        is_public=False
    )

    assert success is True
    assert journey_id == 123
```

### Available Fixtures

- `client` - Flask test client
- `app` - Flask app instance
- `authenticated_session` - Logged-in user session
- `mock_execute_query` - Mock database queries
- `sample_user`, `sample_journey`, `sample_event` - Test data

## 🌐 Browser Testing

### Choose Your Browser Tool

**Python Playwright (Recommended)**

- Modern, fast, reliable
- Better debugging tools
- Automatic waiting

**Selenium**

- Traditional, well-established
- More browser support
- Familiar to many developers

### Running Browser Tests

```bash
# Playwright browser tests
python scripts/test_manager.py browser --browser playwright

# Selenium browser tests
python scripts/test_manager.py browser --browser selenium

# Specific category
python scripts/test_manager.py browser --category auth
```

### Writing Browser Tests

#### Playwright Example

```python
# browser_tests/auth/test_login.py
import pytest

@pytest.mark.browser
@pytest.mark.auth
def test_login_workflow(playwright_page, base_url, test_user_credentials):
    """Test complete login workflow"""
    page = playwright_page

    # Navigate to login page
    page.goto(f"{base_url}/auth/login")

    # Fill login form
    page.fill('input[name="username"]', test_user_credentials["username"])
    page.fill('input[name="password"]', test_user_credentials["password"])
    page.click('button[type="submit"]')

    # Verify successful login
    page.wait_for_url(lambda url: "login" not in url.lower())
    assert "dashboard" in page.url or "journey" in page.url
```

#### Selenium Example

```python
# browser_tests/auth/test_login.py
import pytest
from selenium.webdriver.common.by import By

@pytest.mark.browser
@pytest.mark.auth
def test_login_workflow_selenium(selenium_driver, base_url, test_user_credentials):
    """Test complete login workflow with Selenium"""
    driver = selenium_driver

    # Navigate to login page
    driver.get(f"{base_url}/auth/login")

    # Fill login form
    driver.find_element(By.NAME, "username").send_keys(test_user_credentials["username"])
    driver.find_element(By.NAME, "password").send_keys(test_user_credentials["password"])
    driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()

    # Verify successful login
    assert "login" not in driver.current_url
```

### Browser Test Categories

- **auth/** - Login, registration, logout
- **journeys/** - Journey creation, editing, viewing
- **events/** - Event management, location selection
- **admin/** - Admin panel functionality

## 🔗 Integration Testing

### Running Integration Tests

```bash
# All integration tests
python scripts/test_manager.py integration

# Specific category
python scripts/test_manager.py integration --category api
python scripts/test_manager.py integration --category workflows
```

### Writing Integration Tests

```python
# integration_tests/workflows/test_journey_workflow.py
import pytest

@pytest.mark.integration
@pytest.mark.journey
def test_complete_journey_workflow(integration_client):
    """Test complete journey creation workflow"""
    client = integration_client

    # Login
    response = client.post('/auth/login', data={
        'username': 'testuser',
        'password': 'testpass'
    })
    assert response.status_code in [200, 302]

    # Create journey
    response = client.post('/journey/private/new', data={
        'title': 'Integration Test Journey',
        'description': 'Created in integration test',
        'start_date': '2025-01-01'
    })
    assert response.status_code in [200, 302]

    # Verify journey exists
    response = client.get('/journey/private')
    assert b'Integration Test Journey' in response.data
```

## 📊 Test Reporting

### Coverage Reports

```bash
# Generate coverage report
python scripts/test_manager.py coverage

# View HTML coverage report
open reports/coverage/index.html
```

### HTML Test Reports

```bash
# Generate HTML test report
python scripts/test_manager.py html-report

# View report
open reports/html/test_report_*.html
```

### Test Markers

Use markers to organize and run specific test types:

```bash
# Run by marker
python scripts/test_manager.py --marker auth
python scripts/test_manager.py --marker journey
python scripts/test_manager.py --marker slow
```

Available markers:

- `unit`, `browser`, `integration` - Test types
- `auth`, `journey`, `event`, `admin` - Feature areas
- `slow`, `database`, `api` - Special categories

## ⚡ Advanced Features

### Parallel Testing

```bash
# Run tests in parallel
python scripts/test_manager.py parallel

# Specify number of workers
python scripts/test_manager.py parallel --workers 4
```

### Environment Configuration

Create `.env.testing` file:

```bash
# Browser settings
BROWSER_TYPE=chromium
HEADLESS=true
TEST_BASE_URL=http://127.0.0.1:5000

# Test database
TEST_DB_NAME=travel_journal_test

# Timeouts
DEFAULT_TIMEOUT=30
BROWSER_TIMEOUT=60
```

### Custom Test Data

Create fixtures in `fixtures/`:

```json
// fixtures/test_users.json
{
  "test_user": {
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpass"
  }
}
```

## 🛠️ Test Management Commands

### Command Line Interface

```bash
# Basic commands
python scripts/test_manager.py unit
python scripts/test_manager.py browser
python scripts/test_manager.py integration
python scripts/test_manager.py all

# With options
python scripts/test_manager.py browser --browser playwright --category auth
python scripts/test_manager.py unit --category services
python scripts/test_manager.py parallel --workers 4

# Utilities
python scripts/test_manager.py list          # List all tests
python scripts/test_manager.py install      # Install dependencies
python scripts/test_manager.py setup-playwright  # Setup Playwright
```

### Interactive Menu

```bash
python scripts/test_manager.py menu
```

Provides a user-friendly menu for all testing operations.

### Convenience Scripts

**Windows:**

```bash
run_tests.bat
```

**Unix/Linux/Mac:**

```bash
./run_tests.sh
```

## 🎯 Best Practices

### Test Organization

1. **Use descriptive test names**
2. **Group related tests in classes**
3. **Use appropriate markers**
4. **Keep tests independent**

### Mocking Strategy

1. **Mock external dependencies**
2. **Mock database calls in unit tests**
3. **Use real database for integration tests**
4. **Mock slow or unreliable services**

### Browser Testing Tips

1. **Use explicit waits, not sleep()**
2. **Test critical user workflows**
3. **Keep tests focused and atomic**
4. **Use page object pattern for complex pages**

### Performance

1. **Run unit tests frequently**
2. **Run browser tests before commits**
3. **Use parallel execution for large test suites**
4. **Profile slow tests and optimize**

## 🆘 Troubleshooting

### Common Issues

1. **Import errors**: Check Python path in conftest.py
2. **Browser not found**: Run `python scripts/test_manager.py setup-playwright`
3. **Database errors**: Verify MySQL connection and test data
4. **Timeout errors**: Increase timeouts in pytest.ini

### Debug Mode

```bash
# Run with verbose output
pytest -v -s

# Run single test with debugging
pytest browser_tests/auth/test_login.py::test_login_workflow -v -s

# Use pytest debugger
pytest --pdb
```

## 🎉 Summary

This Python testing suite provides:

✅ **100% Python** - No JavaScript required
✅ **Comprehensive Coverage** - Unit, browser, integration tests
✅ **Modern Tools** - Playwright, pytest, coverage
✅ **Easy Management** - Interactive menus and CLI
✅ **Detailed Reporting** - HTML reports and coverage
✅ **Parallel Execution** - Fast test runs
✅ **MySQL Integration** - Matches your database
✅ **Flask Integration** - Works with your app structure

You now have a professional-grade testing framework that's completely Python-based and tailored to your COMP639 project!
