# 🧪 Comprehensive Testing Guide

This guide covers all aspects of testing for the COMP639 Project webapp using the separated testing suite.

## 🎯 Overview

The testing suite is completely separated from the main application code and provides:

- **🎭 End-to-End Testing** with Playwright
- **🔬 Unit Testing** with Pytest
- **📈 Function Analysis** for code optimization
- **📊 Comprehensive Reporting** with coverage analysis
- **🤖 Automated Test Recording** from user interactions

## 🚀 Quick Start

### 1. Setup Testing Environment

```bash
cd testing
python scripts/setup_testing.py
```

This will:
- Install all dependencies (Python + Node.js)
- Install Playwright browsers
- Migrate existing tests
- Create example tests
- Set up convenience scripts

### 2. Record Your First Test

```bash
python scripts/record_interactions.py
```

Choose from the interactive menu:
- Record basic interactions (no login)
- Record authenticated interactions (with login)
- Organize tests by category (auth/journeys/events/admin)

### 3. Run Tests

```bash
# Run all tests
python scripts/test_manager.py run-all

# Run specific test types
python scripts/test_manager.py run-playwright
python scripts/test_manager.py run-unit

# Run tests by category
python scripts/test_manager.py run-playwright --category auth
```

## 📁 Directory Structure

```
testing/
├── scripts/                    # Automation scripts
│   ├── record_interactions.py  # Interactive test recorder
│   ├── test_manager.py        # Test management utilities
│   ├── analyze_functions.py   # Function analysis
│   └── setup_testing.py       # Environment setup
│
├── playwright/                 # E2E tests
│   ├── tests/
│   │   ├── auth/              # Authentication tests
│   │   ├── journeys/          # Journey functionality
│   │   ├── events/            # Event management
│   │   ├── admin/             # Admin features
│   │   └── general/           # General functionality
│   ├── fixtures/              # Test data
│   ├── utils/                 # Testing utilities
│   └── auth.json             # Saved authentication
│
├── unit/                      # Python unit tests
│   ├── conftest.py           # Pytest configuration
│   ├── data/                 # Data layer tests
│   ├── services/             # Service layer tests
│   ├── routes/               # Route tests
│   └── utils/                # Utility tests
│
├── reports/                   # Test reports
│   ├── playwright/           # E2E test reports
│   ├── coverage/             # Code coverage
│   ├── function-analysis/    # Function usage
│   └── screenshots/          # Test screenshots
│
├── data/                     # Test data files
└── docs/                     # Documentation
```

## 🎭 Playwright E2E Testing

### Recording Tests

1. **Start Recording**:
   ```bash
   python scripts/record_interactions.py
   ```

2. **Choose Test Type**:
   - Basic (no authentication required)
   - Authenticated (login required)

3. **Select Category**:
   - `auth` - Login, registration, password reset
   - `journeys` - Journey creation, editing, viewing
   - `events` - Event management, location selection
   - `admin` - Admin panel, user management
   - `general` - Homepage, navigation, search

4. **Perform Actions**: Browser opens, interact naturally
5. **Save Test**: Close browser to save recorded test

### Running Playwright Tests

```bash
# All Playwright tests
npm test

# Specific category
npm run test:auth
npm run test:journeys
npm run test:events
npm run test:admin

# With browser visible
npm run test:headed

# Debug mode
npm run test:debug

# UI mode (interactive)
npm run test:ui
```

### Test Organization

Tests are organized by functionality:

```javascript
// auth/login.spec.js
test.describe('User Authentication', () => {
  test('successful login', async ({ page }) => {
    // Test implementation
  });
});

// journeys/create.spec.js  
test.describe('Journey Management', () => {
  test('create new journey', async ({ page }) => {
    // Test implementation
  });
});
```

## 🔬 Unit Testing

### Running Unit Tests

```bash
# All unit tests
python scripts/test_manager.py run-unit

# Specific category
python scripts/test_manager.py run-unit --category services
python scripts/test_manager.py run-unit --category routes
python scripts/test_manager.py run-unit --category data

# With coverage
pytest unit/ --cov=../services --cov=../data --cov=../utils --cov-report=html
```

### Writing Unit Tests

```python
# unit/services/test_journey_service.py
def test_create_journey_success(mock_journey_data, mock_user_data):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False}
    mock_journey_data.create_journey.return_value = 1
    
    success, message, journey_id = create_journey(
        1, "Test Journey", "Description", "2025-01-01", False
    )
    
    assert success
    assert message == "Journey created successfully"
    assert journey_id == 1
```

### Test Categories

- **Data Layer**: Database operations, data validation
- **Service Layer**: Business logic, data processing
- **Routes**: HTTP endpoints, request handling
- **Utils**: Helper functions, utilities

## 📈 Function Analysis

### Analyzing Template Functions

```bash
python scripts/analyze_functions.py
```

This analyzes JavaScript functions in your templates and:
- Identifies all function declarations
- Finds onclick handlers and event listeners
- Categorizes likely used vs potentially unused functions
- Generates detailed reports with recommendations

### Function Usage Tracking

1. **Static Analysis**: Scans template files for function definitions
2. **Runtime Tracking**: Use browser debug tools during testing
3. **Playwright Integration**: Record which functions are called during tests
4. **Cleanup Recommendations**: Identifies safe-to-remove functions

## 📊 Reporting and Analysis

### Generate Comprehensive Reports

```bash
python scripts/test_manager.py report
```

This generates:
- **Playwright Results**: Test pass/fail status, screenshots, videos
- **Unit Test Coverage**: Code coverage analysis with HTML reports
- **Function Analysis**: JavaScript function usage analysis
- **Performance Metrics**: Test execution times and statistics

### View Reports

```bash
# Playwright HTML report
npm run show-report

# Coverage report
open reports/coverage/index.html

# Function analysis
ls reports/function-analysis/
```

## 🛠️ Advanced Features

### Authenticated Testing

1. **Save Authentication State**:
   ```bash
   npx playwright codegen --save-storage=playwright/auth.json http://127.0.0.1:5000
   ```

2. **Use in Tests**:
   ```javascript
   test.use({ storageState: 'playwright/auth.json' });
   ```

### Custom Test Data

Create test fixtures in `data/`:
```json
// data/test_users.json
{
  "admin_user": {
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### Page Object Model

Organize complex tests with page objects:
```javascript
// playwright/utils/LoginPage.js
class LoginPage {
  constructor(page) {
    this.page = page;
    this.usernameInput = page.locator('input[name="username"]');
    this.passwordInput = page.locator('input[name="password"]');
  }
  
  async login(username, password) {
    await this.usernameInput.fill(username);
    await this.passwordInput.fill(password);
    await this.page.click('button[type="submit"]');
  }
}
```

## 🔧 Configuration

### Playwright Configuration

Key settings in `playwright.config.js`:
- **Base URL**: Points to your Flask app
- **Test Directory**: `./playwright/tests`
- **Reports**: HTML, JSON, screenshots
- **Browsers**: Chrome, Firefox, Safari, Mobile
- **Auto-start**: Automatically starts Flask app

### Pytest Configuration

Settings in `unit/conftest.py`:
- **Flask App**: Test instance with TESTING=True
- **Test Client**: HTTP request simulation
- **Mock Fixtures**: Database and service mocking
- **Path Setup**: Imports from main application

## 🎯 Best Practices

### Test Organization

1. **Separate by Feature**: Group tests by functionality
2. **Use Descriptive Names**: Clear test and file names
3. **Maintain Test Data**: Keep fixtures up to date
4. **Regular Cleanup**: Remove obsolete tests

### Recording Quality Tests

1. **Complete Workflows**: Record full user journeys
2. **Error Scenarios**: Test validation and error handling
3. **Different Roles**: Test with various user permissions
4. **Mobile Testing**: Include mobile viewport tests

### Maintenance

1. **Regular Updates**: Keep dependencies current
2. **Report Review**: Analyze test reports regularly
3. **Function Cleanup**: Remove unused JavaScript functions
4. **Performance Monitoring**: Track test execution times

## 🆘 Troubleshooting

### Common Issues

1. **Flask App Not Starting**: Check port 5000 availability
2. **Tests Timing Out**: Increase timeout in configuration
3. **Authentication Issues**: Re-record auth state
4. **Import Errors**: Check Python path in conftest.py

### Debug Tools

1. **Playwright Debug**: `npm run test:debug`
2. **Playwright UI**: `npm run test:ui`
3. **Pytest Verbose**: `pytest -v`
4. **Coverage Reports**: Check what's not tested

### Getting Help

1. **Documentation**: Check other guides in `docs/`
2. **Test Manager**: `python scripts/test_manager.py --help`
3. **Playwright Docs**: https://playwright.dev
4. **Pytest Docs**: https://pytest.org

---

This separated testing suite provides a professional, maintainable approach to testing your webapp while keeping testing code completely separate from application code.
