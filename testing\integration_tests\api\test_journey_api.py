"""
Integration tests for Journey API endpoints

Tests the complete API functionality including request/response handling,
authentication, data validation, and error responses.
"""

import pytest
import json
from flask import url_for

@pytest.mark.integration
@pytest.mark.api
@pytest.mark.journey
class TestJourneyAPIEndpoints:
    """Test Journey API endpoints with real database integration"""
    
    def test_api_get_journeys_authenticated(self, integration_client):
        """Test GET /api/journeys with authenticated user"""
        client = integration_client
        
        # First login to get authenticated session
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Test API endpoint
        response = client.get('/api/journeys')
        
        # Should return JSON response
        assert response.status_code == 200
        assert response.content_type == 'application/json'
        
        data = json.loads(response.data)
        assert isinstance(data, list)
    
    def test_api_get_journeys_unauthenticated(self, integration_client):
        """Test GET /api/journeys without authentication"""
        client = integration_client
        
        response = client.get('/api/journeys')
        
        # Should require authentication
        assert response.status_code in [401, 403, 302]
    
    def test_api_create_journey(self, integration_client):
        """Test POST /api/journeys to create new journey"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Create journey via API
        journey_data = {
            'title': 'API Test Journey',
            'description': 'Created via API integration test',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        response = client.post('/api/journeys',
            data=json.dumps(journey_data),
            content_type='application/json'
        )
        
        # Should create successfully
        assert response.status_code in [200, 201]
        assert response.content_type == 'application/json'
        
        response_data = json.loads(response.data)
        assert 'id' in response_data or 'journey_id' in response_data
    
    def test_api_get_specific_journey(self, integration_client):
        """Test GET /api/journeys/<id> for specific journey"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Create a journey first
        journey_data = {
            'title': 'Test Journey for API',
            'description': 'Test description',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/api/journeys',
            data=json.dumps(journey_data),
            content_type='application/json'
        )
        
        if create_response.status_code in [200, 201]:
            create_data = json.loads(create_response.data)
            journey_id = create_data.get('id') or create_data.get('journey_id')
            
            if journey_id:
                # Get the specific journey
                response = client.get(f'/api/journeys/{journey_id}')
                
                assert response.status_code == 200
                assert response.content_type == 'application/json'
                
                data = json.loads(response.data)
                assert data['title'] == 'Test Journey for API'
    
    def test_api_update_journey(self, integration_client):
        """Test PUT /api/journeys/<id> to update journey"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Create a journey first
        journey_data = {
            'title': 'Original Title',
            'description': 'Original description',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/api/journeys',
            data=json.dumps(journey_data),
            content_type='application/json'
        )
        
        if create_response.status_code in [200, 201]:
            create_data = json.loads(create_response.data)
            journey_id = create_data.get('id') or create_data.get('journey_id')
            
            if journey_id:
                # Update the journey
                update_data = {
                    'title': 'Updated Title',
                    'description': 'Updated description',
                    'visibility': 'public'
                }
                
                response = client.put(f'/api/journeys/{journey_id}',
                    data=json.dumps(update_data),
                    content_type='application/json'
                )
                
                assert response.status_code == 200
                assert response.content_type == 'application/json'
    
    def test_api_delete_journey(self, integration_client):
        """Test DELETE /api/journeys/<id> to delete journey"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Create a journey first
        journey_data = {
            'title': 'Journey to Delete',
            'description': 'This will be deleted',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/api/journeys',
            data=json.dumps(journey_data),
            content_type='application/json'
        )
        
        if create_response.status_code in [200, 201]:
            create_data = json.loads(create_response.data)
            journey_id = create_data.get('id') or create_data.get('journey_id')
            
            if journey_id:
                # Delete the journey
                response = client.delete(f'/api/journeys/{journey_id}')
                
                assert response.status_code in [200, 204]


@pytest.mark.integration
@pytest.mark.api
class TestAPIErrorHandling:
    """Test API error handling and validation"""
    
    def test_api_invalid_json(self, integration_client):
        """Test API with invalid JSON data"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Send invalid JSON
        response = client.post('/api/journeys',
            data="invalid json data",
            content_type='application/json'
        )
        
        assert response.status_code == 400
    
    def test_api_missing_required_fields(self, integration_client):
        """Test API with missing required fields"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Send incomplete data
        incomplete_data = {
            'description': 'Missing title and other required fields'
        }
        
        response = client.post('/api/journeys',
            data=json.dumps(incomplete_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        
        if response.content_type == 'application/json':
            error_data = json.loads(response.data)
            assert 'error' in error_data or 'message' in error_data
    
    def test_api_nonexistent_journey(self, integration_client):
        """Test API with nonexistent journey ID"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Try to get nonexistent journey
        response = client.get('/api/journeys/99999')
        
        assert response.status_code == 404
    
    def test_api_unauthorized_access(self, integration_client):
        """Test API access to other user's journey"""
        client = integration_client
        
        # Login as one user
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Try to access journey that doesn't belong to user
        # (This assumes journey ID 1 exists but belongs to different user)
        response = client.get('/api/journeys/1')
        
        # Should either return 403 (forbidden) or 404 (not found)
        assert response.status_code in [403, 404]


@pytest.mark.integration
@pytest.mark.api
class TestAPIPerformance:
    """Test API performance and limits"""
    
    def test_api_response_time(self, integration_client):
        """Test API response time is reasonable"""
        import time
        
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Measure API response time
        start_time = time.time()
        response = client.get('/api/journeys')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # API should respond within 2 seconds
        assert response_time < 2.0
        assert response.status_code == 200
    
    def test_api_large_dataset_handling(self, integration_client):
        """Test API handling of large datasets"""
        client = integration_client
        
        # Login first
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        
        # Test with pagination parameters
        response = client.get('/api/journeys?limit=100&offset=0')
        
        assert response.status_code == 200
        
        if response.content_type == 'application/json':
            data = json.loads(response.data)
            # Should handle large requests gracefully
            assert isinstance(data, list)
            assert len(data) <= 100  # Should respect limit
