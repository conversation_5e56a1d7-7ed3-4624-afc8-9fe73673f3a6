"""
Database integration tests

Tests the complete database integration including real database operations,
transaction handling, and data consistency.
"""

import pytest
from datetime import datetime, date

@pytest.mark.integration
@pytest.mark.database
class TestDatabaseIntegration:
    """Test database integration with real database operations"""
    
    def test_database_connection(self, integration_app):
        """Test that database connection works"""
        with integration_app.app_context():
            try:
                from utils.db_utils import get_db_connection
                
                connection = get_db_connection()
                assert connection is not None
                
                # Test basic query
                cursor = connection.cursor()
                cursor.execute("SELECT 1 as test")
                result = cursor.fetchone()
                
                assert result is not None
                cursor.close()
                connection.close()
                
            except Exception as e:
                pytest.skip(f"Database connection failed: {e}")
    
    def test_journey_crud_operations(self, integration_app):
        """Test complete CRUD operations for journeys"""
        with integration_app.app_context():
            try:
                from services.journey_service import (
                    create_journey, get_journey, update_journey, delete_journey
                )
                
                # Create journey
                success, message, journey_id = create_journey(
                    user_id=1,
                    title="Integration Test Journey",
                    description="Created in integration test",
                    start_date="2025-01-01",
                    visibility="private"
                )
                
                if success and journey_id:
                    # Read journey
                    success, message, journey = get_journey(journey_id, user_id=1)
                    assert success is True
                    assert journey['title'] == "Integration Test Journey"
                    
                    # Update journey
                    success, message = update_journey(
                        journey_id=journey_id,
                        user_id=1,
                        title="Updated Integration Test Journey",
                        description="Updated in integration test",
                        visibility="public"
                    )
                    assert success is True
                    
                    # Verify update
                    success, message, updated_journey = get_journey(journey_id, user_id=1)
                    assert updated_journey['title'] == "Updated Integration Test Journey"
                    
                    # Delete journey
                    success, message = delete_journey(journey_id, user_id=1)
                    assert success is True
                    
                    # Verify deletion
                    success, message, deleted_journey = get_journey(journey_id, user_id=1)
                    assert success is False or deleted_journey is None
                
            except ImportError:
                pytest.skip("Journey service not available")
            except Exception as e:
                pytest.skip(f"Database operation failed: {e}")
    
    def test_user_authentication_flow(self, integration_app):
        """Test complete user authentication with database"""
        with integration_app.app_context():
            try:
                from services.auth_service import register_user, authenticate_user
                
                # Register new user
                success, message, user_id = register_user(
                    username="integrationtest",
                    email="<EMAIL>",
                    password="testpassword123",
                    confirm_password="testpassword123",
                    first_name="Integration",
                    last_name="Test"
                )
                
                if success and user_id:
                    # Authenticate user
                    auth_success, auth_message, user_data = authenticate_user(
                        username="integrationtest",
                        password="testpassword123"
                    )
                    
                    assert auth_success is True
                    assert user_data['username'] == "integrationtest"
                    assert user_data['email'] == "<EMAIL>"
                    
                    # Clean up - delete test user
                    from utils.db_utils import execute_query
                    execute_query(
                        "DELETE FROM users WHERE id = %s",
                        params=(user_id,),
                        commit=True
                    )
                
            except ImportError:
                pytest.skip("Auth service not available")
            except Exception as e:
                pytest.skip(f"Authentication test failed: {e}")
    
    def test_data_consistency(self, integration_app):
        """Test data consistency across related tables"""
        with integration_app.app_context():
            try:
                from services.journey_service import create_journey, delete_journey
                from services.event_service import create_event
                from utils.db_utils import execute_query
                
                # Create journey
                success, message, journey_id = create_journey(
                    user_id=1,
                    title="Consistency Test Journey",
                    description="Testing data consistency",
                    start_date="2025-01-01",
                    visibility="private"
                )
                
                if success and journey_id:
                    # Create event for journey
                    event_success, event_message, event_id = create_event(
                        journey_id=journey_id,
                        user_id=1,
                        location_name="Test Location",
                        title="Test Event",
                        description="Test event description",
                        start_datetime="2025-01-01T10:00:00"
                    )
                    
                    if event_success and event_id:
                        # Verify event exists
                        event_result = execute_query(
                            "SELECT * FROM events WHERE id = %s",
                            params=(event_id,),
                            fetch_one=True
                        )
                        assert event_result is not None
                        assert event_result['journey_id'] == journey_id
                        
                        # Delete journey (should handle related events)
                        delete_success, delete_message = delete_journey(journey_id, user_id=1)
                        
                        if delete_success:
                            # Verify journey is deleted
                            journey_result = execute_query(
                                "SELECT * FROM journeys WHERE id = %s",
                                params=(journey_id,),
                                fetch_one=True
                            )
                            assert journey_result is None
                            
                            # Verify related events are handled appropriately
                            event_result = execute_query(
                                "SELECT * FROM events WHERE journey_id = %s",
                                params=(journey_id,),
                                fetch_all=True
                            )
                            # Events should either be deleted or marked as orphaned
                            assert len(event_result) == 0 or all(
                                event.get('journey_id') is None for event in event_result
                            )
                
            except ImportError:
                pytest.skip("Required services not available")
            except Exception as e:
                pytest.skip(f"Data consistency test failed: {e}")


@pytest.mark.integration
@pytest.mark.database
class TestDatabasePerformance:
    """Test database performance and optimization"""
    
    def test_query_performance(self, integration_app):
        """Test that database queries perform within acceptable limits"""
        import time
        
        with integration_app.app_context():
            try:
                from utils.db_utils import execute_query
                
                # Test simple query performance
                start_time = time.time()
                result = execute_query(
                    "SELECT COUNT(*) as count FROM journeys",
                    fetch_one=True
                )
                end_time = time.time()
                
                query_time = end_time - start_time
                
                # Query should complete within 1 second
                assert query_time < 1.0
                assert result is not None
                
            except Exception as e:
                pytest.skip(f"Performance test failed: {e}")
    
    def test_concurrent_operations(self, integration_app):
        """Test database handling of concurrent operations"""
        import threading
        import time
        
        with integration_app.app_context():
            try:
                from services.journey_service import create_journey
                
                results = []
                errors = []
                
                def create_test_journey(thread_id):
                    try:
                        success, message, journey_id = create_journey(
                            user_id=1,
                            title=f"Concurrent Test Journey {thread_id}",
                            description=f"Created by thread {thread_id}",
                            start_date="2025-01-01",
                            visibility="private"
                        )
                        results.append((thread_id, success, journey_id))
                    except Exception as e:
                        errors.append((thread_id, str(e)))
                
                # Create multiple threads
                threads = []
                for i in range(5):
                    thread = threading.Thread(target=create_test_journey, args=(i,))
                    threads.append(thread)
                
                # Start all threads
                for thread in threads:
                    thread.start()
                
                # Wait for all threads to complete
                for thread in threads:
                    thread.join()
                
                # Verify results
                assert len(errors) == 0, f"Concurrent operations failed: {errors}"
                assert len(results) == 5
                
                # Clean up created journeys
                from services.journey_service import delete_journey
                for thread_id, success, journey_id in results:
                    if success and journey_id:
                        delete_journey(journey_id, user_id=1)
                
            except ImportError:
                pytest.skip("Journey service not available")
            except Exception as e:
                pytest.skip(f"Concurrent operations test failed: {e}")


@pytest.mark.integration
@pytest.mark.database
class TestDatabaseTransactions:
    """Test database transaction handling"""
    
    def test_transaction_rollback(self, integration_app):
        """Test that failed transactions are properly rolled back"""
        with integration_app.app_context():
            try:
                from utils.db_utils import execute_query, get_db_connection
                
                connection = get_db_connection()
                cursor = connection.cursor()
                
                try:
                    # Start transaction
                    cursor.execute("START TRANSACTION")
                    
                    # Insert test data
                    cursor.execute(
                        "INSERT INTO journeys (user_id, title, description, start_date, visibility) VALUES (%s, %s, %s, %s, %s)",
                        (1, "Transaction Test", "Test description", "2025-01-01", "private")
                    )
                    
                    journey_id = cursor.lastrowid
                    
                    # Verify data exists in transaction
                    cursor.execute("SELECT * FROM journeys WHERE id = %s", (journey_id,))
                    result = cursor.fetchone()
                    assert result is not None
                    
                    # Force rollback
                    connection.rollback()
                    
                    # Verify data was rolled back
                    cursor.execute("SELECT * FROM journeys WHERE id = %s", (journey_id,))
                    result = cursor.fetchone()
                    assert result is None
                    
                finally:
                    cursor.close()
                    connection.close()
                
            except Exception as e:
                pytest.skip(f"Transaction test failed: {e}")
    
    def test_transaction_commit(self, integration_app):
        """Test that successful transactions are properly committed"""
        with integration_app.app_context():
            try:
                from utils.db_utils import execute_query
                
                # Create journey with transaction
                journey_id = execute_query(
                    "INSERT INTO journeys (user_id, title, description, start_date, visibility) VALUES (%s, %s, %s, %s, %s)",
                    params=(1, "Commit Test Journey", "Test description", "2025-01-01", "private"),
                    commit=True
                )
                
                assert journey_id is not None
                
                # Verify data persists in new connection
                result = execute_query(
                    "SELECT * FROM journeys WHERE id = %s",
                    params=(journey_id,),
                    fetch_one=True
                )
                
                assert result is not None
                assert result['title'] == "Commit Test Journey"
                
                # Clean up
                execute_query(
                    "DELETE FROM journeys WHERE id = %s",
                    params=(journey_id,),
                    commit=True
                )
                
            except Exception as e:
                pytest.skip(f"Transaction commit test failed: {e}")
