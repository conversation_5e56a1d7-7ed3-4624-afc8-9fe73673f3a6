"""
User workflow integration tests

Tests complete user workflows from start to finish, including:
- User registration and login
- Journey creation and management
- Event addition and editing
- File uploads and image handling
"""

import pytest
import tempfile
import os
from io import BytesIO

@pytest.mark.integration
@pytest.mark.workflow
class TestUserRegistrationWorkflow:
    """Test complete user registration and login workflow"""
    
    def test_complete_registration_login_workflow(self, integration_client):
        """Test user registration followed by login"""
        client = integration_client
        
        # Step 1: Access registration page
        response = client.get('/register')
        assert response.status_code == 200
        assert b'register' in response.data.lower()
        
        # Step 2: Submit registration
        registration_data = {
            'username': 'workflowtest',
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'confirm_password': 'testpassword123',
            'first_name': 'Workflow',
            'last_name': 'Test',
            'location': 'Test City'
        }
        
        response = client.post('/register', data=registration_data)
        
        # Should redirect or show success
        assert response.status_code in [200, 302]
        
        # Step 3: Login with new account
        login_response = client.post('/login', data={
            'username': 'workflowtest',
            'password': 'testpassword123'
        })
        
        # Should redirect to dashboard
        assert login_response.status_code in [200, 302]
        
        # Step 4: Access protected page
        dashboard_response = client.get('/dashboard')
        assert dashboard_response.status_code == 200
        
        # Step 5: Logout
        logout_response = client.get('/logout')
        assert logout_response.status_code == 302
        
        # Step 6: Verify logout by accessing protected page
        protected_response = client.get('/journey/private')
        assert protected_response.status_code == 302  # Should redirect to login


@pytest.mark.integration
@pytest.mark.workflow
@pytest.mark.journey
class TestJourneyManagementWorkflow:
    """Test complete journey management workflow"""
    
    def test_complete_journey_lifecycle(self, integration_client):
        """Test journey creation, viewing, editing, and deletion"""
        client = integration_client
        
        # Step 1: Login
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Access journey list
        list_response = client.get('/journey/private')
        assert list_response.status_code == 200
        
        # Step 3: Access journey creation form
        create_form_response = client.get('/journey/private/new')
        assert create_form_response.status_code == 200
        assert b'title' in create_form_response.data.lower()
        
        # Step 4: Create new journey
        journey_data = {
            'title': 'Workflow Test Journey',
            'description': 'Created in workflow integration test',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/journey/private/new', data=journey_data)
        assert create_response.status_code in [200, 302]
        
        # Step 5: Find the created journey (assuming redirect to journey list)
        if create_response.status_code == 302:
            list_response = client.get('/journey/private')
            assert b'Workflow Test Journey' in list_response.data
        
        # For the remaining steps, we'll need to extract the journey ID
        # This is a simplified version - in practice, you'd parse the response
        # to get the actual journey ID
        
        # Step 6: View journey details (assuming journey ID 1 for demo)
        detail_response = client.get('/journey/private/1')
        if detail_response.status_code == 200:
            assert b'Workflow Test Journey' in detail_response.data or b'journey' in detail_response.data.lower()
        
        # Step 7: Edit journey
        edit_form_response = client.get('/journey/private/1/edit')
        if edit_form_response.status_code == 200:
            edit_data = {
                'title': 'Updated Workflow Test Journey',
                'description': 'Updated in workflow test',
                'start_date': '2025-02-01',
                'visibility': 'public'
            }
            
            edit_response = client.post('/journey/private/1/edit', data=edit_data)
            assert edit_response.status_code in [200, 302]
        
        # Step 8: Delete journey
        delete_response = client.post('/journey/private/1/delete')
        # Delete might return 200, 302, or 404 depending on implementation
        assert delete_response.status_code in [200, 302, 404]


@pytest.mark.integration
@pytest.mark.workflow
@pytest.mark.event
class TestEventManagementWorkflow:
    """Test complete event management workflow"""
    
    def test_add_event_to_journey_workflow(self, integration_client):
        """Test adding events to a journey"""
        client = integration_client
        
        # Step 1: Login
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Create a journey first
        journey_data = {
            'title': 'Event Test Journey',
            'description': 'Journey for testing events',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/journey/private/new', data=journey_data)
        assert create_response.status_code in [200, 302]
        
        # Step 3: Access event creation form (assuming journey ID 1)
        event_form_response = client.get('/event/new/1')
        if event_form_response.status_code == 200:
            assert b'event' in event_form_response.data.lower()
            assert b'title' in event_form_response.data.lower()
            
            # Step 4: Create event
            event_data = {
                'title': 'Test Event',
                'description': 'Event created in workflow test',
                'location_name': 'Test Location',
                'start_datetime': '2025-01-01T10:00',
                'end_datetime': '2025-01-01T12:00'
            }
            
            event_create_response = client.post('/event/new/1', data=event_data)
            assert event_create_response.status_code in [200, 302]
        
        # Step 5: View journey with events
        journey_view_response = client.get('/journey/private/1')
        if journey_view_response.status_code == 200:
            # Should show the event in the journey
            assert b'Test Event' in journey_view_response.data or b'event' in journey_view_response.data.lower()


@pytest.mark.integration
@pytest.mark.workflow
class TestFileUploadWorkflow:
    """Test file upload workflows"""
    
    def test_profile_image_upload_workflow(self, integration_client, test_image_file):
        """Test profile image upload workflow"""
        client = integration_client
        
        # Step 1: Login
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Access profile page
        profile_response = client.get('/account/profile')
        if profile_response.status_code == 200:
            # Step 3: Upload profile image
            data = {
                'first_name': 'Test',
                'last_name': 'User',
                'location': 'Test City'
            }
            
            # Add file upload
            data['profile_image'] = (test_image_file, 'test_profile.jpg')
            
            upload_response = client.post('/account/profile', 
                data=data,
                content_type='multipart/form-data'
            )
            
            # Should handle file upload
            assert upload_response.status_code in [200, 302]
    
    def test_event_image_upload_workflow(self, integration_client, test_image_file):
        """Test event image upload workflow"""
        client = integration_client
        
        # Step 1: Login
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Create journey
        journey_data = {
            'title': 'Image Upload Test Journey',
            'description': 'Testing image uploads',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/journey/private/new', data=journey_data)
        assert create_response.status_code in [200, 302]
        
        # Step 3: Create event with image
        event_form_response = client.get('/event/new/1')
        if event_form_response.status_code == 200:
            event_data = {
                'title': 'Event with Image',
                'description': 'Testing image upload',
                'location_name': 'Test Location',
                'start_datetime': '2025-01-01T10:00',
                'event_image': (test_image_file, 'test_event.jpg')
            }
            
            event_response = client.post('/event/new/1',
                data=event_data,
                content_type='multipart/form-data'
            )
            
            assert event_response.status_code in [200, 302]


@pytest.mark.integration
@pytest.mark.workflow
class TestPermissionWorkflows:
    """Test permission and access control workflows"""
    
    def test_private_journey_access_workflow(self, integration_client):
        """Test access control for private journeys"""
        client = integration_client
        
        # Step 1: Login as first user
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Create private journey
        journey_data = {
            'title': 'Private Journey Test',
            'description': 'This should be private',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
        
        create_response = client.post('/journey/private/new', data=journey_data)
        assert create_response.status_code in [200, 302]
        
        # Step 3: Logout
        logout_response = client.get('/logout')
        assert logout_response.status_code == 302
        
        # Step 4: Try to access private journey without login
        private_access_response = client.get('/journey/private/1')
        assert private_access_response.status_code == 302  # Should redirect to login
        
        # Step 5: Login as different user (if available)
        other_login_response = client.post('/login', data={
            'username': 'otheruser',
            'password': 'otherpass'
        })
        
        if other_login_response.status_code in [200, 302]:
            # Step 6: Try to access other user's private journey
            other_access_response = client.get('/journey/private/1')
            # Should either redirect or show 403/404
            assert other_access_response.status_code in [302, 403, 404]
    
    def test_public_journey_access_workflow(self, integration_client):
        """Test access to public journeys"""
        client = integration_client
        
        # Step 1: Login
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
        # Step 2: Create public journey
        journey_data = {
            'title': 'Public Journey Test',
            'description': 'This should be public',
            'start_date': '2025-01-01',
            'visibility': 'public'
        }
        
        create_response = client.post('/journey/private/new', data=journey_data)
        assert create_response.status_code in [200, 302]
        
        # Step 3: Logout
        logout_response = client.get('/logout')
        assert logout_response.status_code == 302
        
        # Step 4: Access public journeys page without login
        public_response = client.get('/journey/public')
        assert public_response.status_code == 200
        
        # Should show public journeys
        assert b'journey' in public_response.data.lower() or b'public' in public_response.data.lower()
