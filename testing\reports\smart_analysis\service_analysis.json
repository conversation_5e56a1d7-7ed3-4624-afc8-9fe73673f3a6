{"account_service": {"functions": {"change_user_password": {"name": "change_user_password", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "current_password": {"type": "<class 'str'>", "default": null, "required": true}, "new_password": {"type": "<class 'str'>", "default": null, "required": true}, "confirm_password": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Change user password.\n\nArgs:\n    user_id: ID of the user\n    current_password: Current password\n    new_password: New password\n    confirm_password: Confirmation of new password\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_profile_image": {"name": "delete_profile_image", "parameters": {"filename": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Delete a profile image from the file system.\n\nArgs:\n    filename: Name of the file to delete\n\nReturns:\n    True if successful, False otherwise", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_by_username": {"name": "get_user_by_username", "parameters": {"username": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get user profile by username.\n\nArgs:\n    username: The username to look up\n\nReturns:\n    User profile dictionary if found, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_profile": {"name": "get_user_profile", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get user's profile data.\n\nArgs:\n    user_id: The ID of the user to get the profile for\n\nReturns:\n    The user's profile data if found, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "remove_user_profile_image": {"name": "remove_user_profile_image", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Remove user profile image.\n\nArgs:\n    user_id: ID of the user\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_user_profile": {"name": "update_user_profile", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "email": {"type": "typing.Optional[str]", "default": "None", "required": false}, "first_name": {"type": "typing.Optional[str]", "default": "None", "required": false}, "last_name": {"type": "typing.Optional[str]", "default": "None", "required": false}, "location": {"type": "typing.Optional[str]", "default": "None", "required": false}, "description": {"type": "typing.Optional[str]", "default": "None", "required": false}, "interests": {"type": "typing.Optional[str]", "default": "None", "required": false}, "is_public": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_first_name": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_last_name": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_email": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_username": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_location": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_description": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_interests": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_recent_likes": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_recent_comments": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_public_journeys": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "show_visited_places": {"type": "typing.Optional[bool]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update user profile information.\n\nArgs:\n    user_id: ID of the user\n    email: New email\n    first_name: New first name\n    last_name: New last name\n    location: New location\n    description: New description\n    interests: User interests (comma-separated)\n    is_public: Whether the profile is publicly visible\n    show_first_name: Whether to show first name on public profile\n    show_last_name: Whether to show last name on public profile\n    show_email: Whether to show email on public profile\n    show_username: Whether to show username on public profile\n    show_location: Whether to show location on public profile\n    show_description: Whether to show description on public profile\n    show_interests: Whether to show interests on public profile\n    show_recent_likes: Whether to show recent likes on public profile\n    show_recent_comments: Whether to show recent comments on public profile\n    show_public_journeys: Whether to show public journeys on public profile\n    show_visited_places: Whether to show visited places on public profile\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_user_profile_image": {"name": "update_user_profile_image", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "file": {"type": "<class 'werkzeug.datastructures.file_storage.FileStorage'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update user profile image.\n\nArgs:\n    user_id: ID of the user\n    file: Image file object\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "announcement_service": {"functions": {"create_announcement": {"name": "create_announcement", "parameters": {"author_id": {"type": "<class 'int'>", "default": null, "required": true}, "title": {"type": "<class 'str'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a new announcement (editor/admin only).\n\nArgs:\n    author_id: ID of the user creating the announcement.\n    title: Title of the announcement.\n    content: Content of the announcement.\n    \nReturns:\n    Tuple[bool, str, Optional[int]]: Tuple containing success flag, message, and announcement ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_announcement": {"name": "delete_announcement", "parameters": {"announcement_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete an announcement (editor/admin only).\n\nArgs:\n    announcement_id: ID of the announcement to delete.\n    user_id: ID of the user performing the deletion.\n    \nReturns:\n    Tuple[bool, str]: Tuple containing success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_all_announcements": {"name": "get_all_announcements", "parameters": {"limit": {"type": "<class 'int'>", "default": "10", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all announcements with pagination (editor/admin only).\n\nArgs:\n    limit: Maximum number of announcements to return.\n    offset: Number of announcements to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of all announcement objects or empty list on error.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_all_announcements_count": {"name": "get_all_announcements_count", "parameters": {}, "return_annotation": "<class 'int'>", "docstring": "Get total count of all announcements.\n\nReturns:\n    int: Total number of announcements in the system or 0 on error.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_announcement": {"name": "get_announcement", "parameters": {"announcement_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a specific announcement by ID.\n\nArgs:\n    announcement_id: ID of the announcement.\n    \nReturns:\n    Dict[str, Any]: Announcement object if found, None otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_read_announcements": {"name": "get_read_announcements", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "10", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get paginated read announcements for a user.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of announcements to return.\n    offset: Number of announcements to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of read announcement objects.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_read_announcements_count": {"name": "get_read_announcements_count", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Get total count of read announcements for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    int: Number of read announcements.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_unread_announcements": {"name": "get_unread_announcements", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "10", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get paginated unread announcements for a user.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of announcements to return.\n    offset: Number of announcements to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of unread announcement objects.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_unread_announcements_count": {"name": "get_unread_announcements_count", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Get total count of unread announcements for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    int: Number of unread announcements.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_announcements": {"name": "get_user_announcements", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "10", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get paginated announcements for a specific user.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of announcements to return.\n    offset: Number of announcements to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of announcement objects or empty list on error.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_announcements_count": {"name": "get_user_announcements_count", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Get total count of announcements for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    int: Total number of announcements or 0 on error.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "mark_announcement_as_read": {"name": "mark_announcement_as_read", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "announcement_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Mark an announcement as read for a specific user.\n\nArgs:\n    user_id: ID of the user.\n    announcement_id: ID of the announcement.\n    \nReturns:\n    Tuple[bool, str]: Tuple containing success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_announcement": {"name": "update_announcement", "parameters": {"announcement_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "title": {"type": "typing.Optional[str]", "default": "None", "required": false}, "content": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update an existing announcement (editor/admin only).\n\nArgs:\n    announcement_id: ID of the announcement to update.\n    user_id: ID of the user making the update.\n    title: Optional new title for the announcement.\n    content: Optional new content for the announcement.\n    \nReturns:\n    Tuple[bool, str]: Tuple containing success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "auth_service": {"functions": {"authenticate_user": {"name": "authenticate_user", "parameters": {"username": {"type": "<class 'str'>", "default": null, "required": true}, "password": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.Dict[str, typing.Any]]]", "docstring": "Authenticate a user with username and password.\n\nArgs:\n    username: <PERSON>rname\n    password: Password\n    \nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - dict or None: User data if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "logout_user": {"name": "logout_user", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Log out a user.\n\nArgs:\n    user_id: ID of the user to log out\n    \nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "register_user": {"name": "register_user", "parameters": {"username": {"type": "<class 'str'>", "default": null, "required": true}, "email": {"type": "<class 'str'>", "default": null, "required": true}, "password": {"type": "<class 'str'>", "default": null, "required": true}, "confirm_password": {"type": "<class 'str'>", "default": null, "required": true}, "first_name": {"type": "typing.Optional[str]", "default": "None", "required": false}, "last_name": {"type": "typing.Optional[str]", "default": "None", "required": false}, "location": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Register a new user.\n\nArgs:\n    username: <PERSON><PERSON><PERSON> for the new user\n    email: Email address for the new user\n    password: Password for the new user\n    confirm_password: Password confirmation\n    first_name: First name\n    last_name: Last name\n    location: User's location\n    \nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int or None: User ID if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "community_service": {"functions": {"add_comment": {"name": "add_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "event_id": {"type": "<class 'int'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Add a comment to an event.\n\nArgs:\n    user_id: ID of the user.\n    event_id: ID of the event.\n    content: Comment text.\n    \nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and comment ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "check_can_message": {"name": "check_can_message", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "recipient_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user can send messages.\n\nArgs:\n    user_id: ID of the user to check.\n    recipient_id: ID of the recipient user.\n    \nReturns:\n    bool: True if the user can send messages, False otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "check_user_interaction": {"name": "check_user_interaction", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "interaction_type": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Check if a user has interacted with a comment in a specific way.\n\nArgs:\n    user_id: ID of the user to check.\n    comment_id: ID of the comment to check.\n    interaction_type: Type of interaction to check ('like', 'dislike', 'report').\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "check_user_liked_event": {"name": "check_user_liked_event", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "count_event_likes": {"name": "count_event_likes", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Count likes for an event.\n\nArgs:\n    event_id: ID of the event.\n    \nReturns:\n    int: Number of likes.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "count_unread_messages": {"name": "count_unread_messages", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Count unread messages for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    int: Number of unread messages.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "delete_comment": {"name": "delete_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete a comment.\n\nArgs:\n    user_id: ID of the user.\n    comment_id: ID of the comment.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_message": {"name": "delete_message", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "message_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete a message.\n\nArgs:\n    user_id: ID of the user.\n    message_id: ID of the message.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_comment": {"name": "get_comment", "parameters": {"comment_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[dict]]", "docstring": "Retrieve a comment by its ID.\n\nArgs:\n    comment_id: ID of the comment to retrieve.\n    \nReturns:\n    Tuple[bool, str, Optional[dict]]: Success flag, message, and the comment data if found.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_conversation": {"name": "get_conversation", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "other_user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get conversation between two users.\n\nArgs:\n    user_id: ID of the current user.\n    other_user_id: ID of the other user.\n    limit: Maximum number of messages to return.\n    offset: Number of messages to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of messages in the conversation.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_event_comments": {"name": "get_event_comments", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "include_hidden": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all comments for an event.\n\nArgs:\n    event_id: ID of the event.\n    include_hidden: Whether to include hidden comments.\n    \nReturns:\n    List[Dict[str, Any]]: List of comments.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_event_likes": {"name": "get_event_likes", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all likes for an event.\n\nArgs:\n    event_id: ID of the event.\n    \nReturns:\n    List[Dict[str, Any]]: List of users who liked the event.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_comments": {"name": "get_user_comments", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all comments made by a user with pagination.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of results to return.\n    offset: Number of results to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of comment records with event and journey information.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_conversations": {"name": "get_user_conversations", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all conversations a user is part of.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    List[Dict[str, Any]]: List of conversation summaries.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_liked_events": {"name": "get_user_liked_events", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all events liked by a user with pagination.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of results to return.\n    offset: Number of results to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of event records with journey and user information.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "interact_with_comment": {"name": "interact_with_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "interaction_type": {"type": "<class 'str'>", "default": null, "required": true}, "report_reason": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Interact with a comment (like, dislike, report).\n\nArgs:\n    user_id: ID of the user.\n    comment_id: ID of the comment.\n    interaction_type: Type of interaction ('like', 'dislike', 'report').\n    report_reason: Reason for report (required for 'report' type).\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "like_event": {"name": "like_event", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Like an event.\n\nArgs:\n    user_id: ID of the user.\n    event_id: ID of the event.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "mark_message_as_read": {"name": "mark_message_as_read", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "message_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Mark a message as read.\n\nArgs:\n    user_id: ID of the user.\n    message_id: ID of the message.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "moderate_comment": {"name": "moderate_comment", "parameters": {"moderator_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "action": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Moderate a comment (hide/unhide).\n\nArgs:\n    moderator_id: ID of the moderator.\n    comment_id: ID of the comment.\n    action: Moderation action ('hide' or 'unhide').\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "remove_comment_interaction": {"name": "remove_comment_interaction", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "interaction_type": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Remove an interaction from a comment.\n\nArgs:\n    user_id: ID of the user.\n    comment_id: ID of the comment.\n    interaction_type: Type of interaction ('like', 'dislike', 'report').\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "send_message": {"name": "send_message", "parameters": {"sender_id": {"type": "<class 'int'>", "default": null, "required": true}, "recipient_id": {"type": "<class 'int'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Send a private message to another user.\n\nArgs:\n    sender_id: ID of the sender.\n    recipient_id: ID of the recipient.\n    content: Message content.\n    \nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and message ID if sent.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "unlike_event": {"name": "unlike_event", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Unlike an event.\n\nArgs:\n    user_id: ID of the user.\n    event_id: ID of the event.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_comment": {"name": "update_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a comment.\n\nArgs:\n    user_id: ID of the user.\n    comment_id: ID of the comment.\n    content: New comment text.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "country_service": {"functions": {"get_countries_for_dropdown": {"name": "get_countries_for_dropdown", "parameters": {}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all countries formatted for dropdown UI.\n\nReturns:\n    List[Dict[str, Any]]: List of countries with id, name, code, and formatted display value.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}}}, "departure_board_service": {"functions": {"check_following_journey": {"name": "check_following_journey", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "journey_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Check if a user is following a journey.\n\nArgs:\n    user_id: ID of the user.\n    journey_id: ID of the journey.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message indicating following status or error.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "check_following_location": {"name": "check_following_location", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "location_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Check if a user is following a location.\n\nArgs:\n    user_id: ID of the user.\n    location_id: ID of the location.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message indicating following status or error.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "check_following_user": {"name": "check_following_user", "parameters": {"follower_id": {"type": "<class 'int'>", "default": null, "required": true}, "followed_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Check if a user is following another user.\n\nArgs:\n    follower_id: ID of the user who may be following.\n    followed_id: ID of the user being followed.\n\nReturns:\n    Tuple[bool, str]: Success flag and message indicating following status or error.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "follow_journey": {"name": "follow_journey", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "journey_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Follow a journey.\n\nArgs:\n    user_id: ID of the user.\n    journey_id: ID of the journey.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "follow_location": {"name": "follow_location", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "location_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Follow a location.\n\nArgs:\n    user_id: ID of the user.\n    location_id: ID of the location.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "follow_user": {"name": "follow_user", "parameters": {"follower_id": {"type": "<class 'int'>", "default": null, "required": true}, "followed_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Follow a user.\n\nArgs:\n    follower_id: ID of the user doing the following.\n    followed_id: ID of the user to follow.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_departure_board": {"name": "get_departure_board", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.List[typing.Dict[str, typing.Any]]]]", "docstring": "Get departure board events for a user.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of events to return.\n    offset: Number of events to skip.\n    \nReturns:\n    Tuple[bool, str, Optional[List[Dict[str, Any]]]]: Success flag, message, and list of departure board events.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_departure_board_count": {"name": "get_departure_board_count", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get total count of user's private journeys with search", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_events_grouped_by": {"name": "get_events_grouped_by", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "group_by": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_followed_journeys": {"name": "get_followed_journeys", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all journeys followed by a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    List[Dict[str, Any]]: List of followed journey records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_followed_locations": {"name": "get_followed_locations", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all locations followed by a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    List[Dict[str, Any]]: List of followed location records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_followed_users": {"name": "get_followed_users", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all users followed by a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    List[Dict[str, Any]]: List of followed user records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_followers": {"name": "get_followers", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all followers of a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    List[Dict[str, Any]]: List of follower records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "unfollow_journey": {"name": "unfollow_journey", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "journey_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Unfollow a journey.\n\nArgs:\n    user_id: ID of the user.\n    journey_id: ID of the journey.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "unfollow_location": {"name": "unfollow_location", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "location_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Unfollow a location.\n\nArgs:\n    user_id: ID of the user.\n    location_id: ID of the location.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "unfollow_user": {"name": "unfollow_user", "parameters": {"follower_id": {"type": "<class 'int'>", "default": null, "required": true}, "followed_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Unfollow a user.\n\nArgs:\n    follower_id: ID of the user doing the unfollowing.\n    followed_id: ID of the user to unfollow.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "edit_history_service": {"functions": {"create_edit_notification": {"name": "create_edit_notification", "parameters": {"content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}, "editor_id": {"type": "<class 'int'>", "default": null, "required": true}, "field_changes": {"type": "typing.Dict[str, typing.Tuple[str, str]]", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[int]", "docstring": "Create a notification for the owner of content that was edited.\n\nArgs:\n    content_type: Type of content that was edited.\n    content_id: ID of the content that was edited.\n    editor_id: ID of the editor who made the edit.\n    field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.\n    reason: The reason for the edit.\n\nReturns:\n    Optional[int]: ID of the newly created notification, or None if creation failed.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_content_edit_history": {"name": "get_content_edit_history", "parameters": {"content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get edit history for a piece of content.\n\nArgs:\n    content_type: Type of content ('journey', 'event', 'location').\n    content_id: ID of the content.\n\nReturns:\n    List[Dict[str, Any]]: List of edit history records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_editor_edit_history": {"name": "get_editor_edit_history", "parameters": {"editor_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get edit history for edits made by a specific editor.\n\nArgs:\n    editor_id: ID of the editor.\n    limit: Maximum number of results to return.\n    offset: Number of results to skip.\n\nReturns:\n    List[Dict[str, Any]]: List of edit history records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_content_edit_history": {"name": "get_user_content_edit_history", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get edit history for all content owned by a user.\n\nArgs:\n    user_id: ID of the user.\n\nReturns:\n    List[Dict[str, Any]]: List of edit history records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "has_edit_history": {"name": "has_edit_history", "parameters": {"content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a piece of content has any edit history.\n\nArgs:\n    content_type: Type of content ('journey', 'event', 'location').\n    content_id: ID of the content.\n\nReturns:\n    bool: True if edit history exists, False otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "record_edit": {"name": "record_edit", "parameters": {"editor_id": {"type": "<class 'int'>", "default": null, "required": true}, "content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}, "field_changes": {"type": "typing.Dict[str, typing.Tuple[str, str]]", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Record an edit to content.\n\nArgs:\n    editor_id: ID of the editor making the change.\n    content_type: Type of content ('journey', 'event', 'location').\n    content_id: ID of the content.\n    field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.\n    reason: Reason for the edit.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and edit ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "event_service": {"functions": {"add_event_comment": {"name": "add_event_comment", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Add a comment to an event.\n\nArgs:\n    event_id: ID of the event the comment belongs to\n    user_id: ID of the user adding the comment\n    content: The content of the comment\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int or None: ID of the created comment, or None if failed", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "add_event_destination": {"name": "add_event_destination", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "location_name": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Add a destination to an event.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user adding the destination\n    location_name: Name of the destination location\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int or None: Destination ID if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "add_event_image": {"name": "add_event_image", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "image": {"type": "<class 'werkzeug.datastructures.file_storage.FileStorage'>", "default": null, "required": true}, "caption": {"type": "typing.Optional[str]", "default": "None", "required": false}, "is_primary": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Add an image to an event.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user adding the image\n    image: Image file\n    caption: Optional caption for the image\n    is_primary: Whether this should be the primary image\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int or None: Image ID if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_event": {"name": "create_event", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "location_name": {"type": "<class 'str'>", "default": null, "required": true}, "title": {"type": "<class 'str'>", "default": null, "required": true}, "description": {"type": "<class 'str'>", "default": null, "required": true}, "start_datetime": {"type": "<class 'datetime.datetime'>", "default": null, "required": true}, "end_datetime": {"type": "typing.Optional[datetime.datetime]", "default": "None", "required": false}, "images": {"type": "typing.Optional[typing.List[werkzeug.datastructures.file_storage.FileStorage]]", "default": "None", "required": false}, "longitude": {"type": "typing.Optional[float]", "default": "None", "required": false}, "latitude": {"type": "typing.Optional[float]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a new event.\n\nArgs:\n    journey_id: ID of the journey\n    user_id: ID of the user creating the event\n    location_name: Name of the location\n    title: Event title\n    description: Event description\n    start_datetime: Start date and time\n    end_datetime: End date and time\n    images: List of event image files\n    longitude: Longitude of location\n    latitude: Latitude of location\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int or None: Event ID if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_event": {"name": "delete_event", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete an event.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user making the deletion\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_event_image": {"name": "delete_event_image", "parameters": {"image_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "edit_reason": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete an event image.\n\nArgs:\n    image_id: ID of the image to delete\n    user_id: ID of the user deleting the image\n    edit_reason: Reason for deletion (required for staff editing other users' content)\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_event_image_by_id": {"name": "delete_event_image_by_id", "parameters": {"image_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Delete an event image by ID\n\nArgs:\n    image_id: ID of the image\n    user_id: ID of the user deleting the image\n\nReturns:\n    Tuple containing success flag, message, and event ID if successful", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_multiple_event_images": {"name": "delete_multiple_event_images", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "image_ids": {"type": "typing.List[int]", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "edit_reason": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, int, int]", "docstring": "Delete multiple event images with a single notification.\n\nArgs:\n    event_id: ID of the event\n    image_ids: List of image IDs to delete\n    user_id: ID of the user deleting the images\n    edit_reason: Reason for deletion (required for staff editing other users' content)\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - int: Number of successfully deleted images\n        - int: Number of failed deletions", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_event": {"name": "get_event", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.Dict[str, typing.Any]]]", "docstring": "Get an event by ID.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user requesting the event\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - dict or None: Event data if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_event_images": {"name": "get_event_images", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.List[typing.Dict[str, typing.Any]]]]", "docstring": "Get all images for an event.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user requesting the images\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - list or None: List of image data if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_event_images_list": {"name": "get_event_images_list", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict]", "docstring": "Get all images for an event\n\nArgs:\n    event_id: ID of the event\n\nReturns:\n    List of image dictionaries", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_events_by_location": {"name": "get_events_by_location", "parameters": {"location_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get events by location.\n\nArgs:\n    location_id: ID of the location\n    limit: Maximum number of events to return\n    offset: Number of events to skip\n\nReturns:\n    List of event objects for the location", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_journey_events": {"name": "get_journey_events", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "typing.Optional[int]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.List[typing.Dict[str, typing.Any]]]]", "docstring": "Get all events for a journey.\n\nArgs:\n    journey_id: ID of the journey\n    user_id: ID of the user requesting the events. If None, only public/published journeys are accessible.\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - list or None: List of event data if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_user_private_events": {"name": "get_user_private_events", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.List[typing.Dict[str, typing.Any]]]]", "docstring": "Get all events for a user.\n\nArgs:\n    user_id: ID of the user requesting the events. If None, only public/published journeys are accessible.\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - list or None: List of event data if successful, None otherwise", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_user_public_events": {"name": "get_user_public_events", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.List[typing.Dict[str, typing.Any]]]]", "docstring": "Get all public events for a user.\n\nArgs:\n    user_id: ID of the user whose public events are requested.\n\nReturns:\n    Tuple containing:\n        - bool: Success status\n        - str: Message describing the result\n        - list or None: List of event data if successful, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "interact_with_comment": {"name": "interact_with_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}, "interaction_type": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Interact with a comment (like, dislike, or report).\n\nArgs:\n    user_id: ID of the user interacting with the comment.\n    comment_id: ID of the comment.\n    interaction_type: Type of interaction ('like', 'dislike', or 'report').\n\nReturns:\n    A tuple containing:\n        - bool: Success status (True or False).\n        - str: Message describing the result.\n        - int: Interaction ID if successful, 0 if already exists, or None if failed.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_event": {"name": "update_event", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "title": {"type": "typing.Optional[str]", "default": "None", "required": false}, "description": {"type": "typing.Optional[str]", "default": "None", "required": false}, "start_datetime": {"type": "typing.Optional[typing.Any]", "default": "None", "required": false}, "end_datetime": {"type": "typing.Optional[typing.Any]", "default": "None", "required": false}, "location_name": {"type": "typing.Optional[str]", "default": "None", "required": false}, "images": {"type": "typing.Optional[typing.Dict[str, typing.Any]]", "default": "None", "required": false}, "edit_reason": {"type": "typing.Optional[str]", "default": "None", "required": false}, "longitude": {"type": "typing.Optional[float]", "default": "None", "required": false}, "latitude": {"type": "typing.Optional[float]", "default": "None", "required": false}, "staff_location_scope": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update event details and handle related images.\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user updating the event\n    title: New title (optional)\n    description: New description (optional)\n    start_datetime: New start datetime (optional)\n    end_datetime: New end datetime (optional)\n    location_name: New location name (optional)\n    images: Dictionary of images to add/delete (optional)\n    edit_reason: Reason for the edit (required for staff edits)\n    longitude: Longitude of the updated location (float)\n    latitude: Latitude of the updated location (float)\n    staff_location_scope: For staff edits - 'all_events' or 'this_event_only' (optional)\n\nReturns:\n    Tuple[bool, str]: Success flag and message", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_event_image": {"name": "update_event_image", "parameters": {"image_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "caption": {"type": "typing.Optional[str]", "default": "None", "required": false}, "is_primary": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "new_image": {"type": "typing.Optional[werkzeug.datastructures.file_storage.FileStorage]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update an event image.\n\nArgs:\n    image_id: ID of the image to update\n    user_id: ID of the user making the update\n    caption: New caption for the image\n    is_primary: Whether this should be the primary image\n    new_image: New image file to replace the existing one\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "upload_event_image": {"name": "upload_event_image", "parameters": {"event_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "file": {"type": "any", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Upload an image for an event\n\nArgs:\n    event_id: ID of the event\n    user_id: ID of the user uploading the image\n    file: File object from request.files\n\nReturns:\n    Tuple containing success flag, message, and image ID if successful", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "helpdesk_service": {"functions": {"abandon_ticket": {"name": "abandon_ticket", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "add_reply_to_ticket": {"name": "add_reply_to_ticket", "parameters": {"userId": {"type": "any", "default": null, "required": true}, "ticket_id": {"type": "any", "default": null, "required": true}, "content": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "add_ticket": {"name": "add_ticket", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "subject": {"type": "<class 'str'>", "default": null, "required": true}, "category": {"type": "<class 'str'>", "default": null, "required": true}, "description": {"type": "<class 'str'>", "default": null, "required": true}, "username": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "assign_ticket": {"name": "assign_ticket", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}, "staff_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "change_ticket_status": {"name": "change_ticket_status", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}, "status": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_banned_user_appeal": {"name": "create_banned_user_appeal", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, int]", "docstring": "Create a ban appeal for a banned user.\n\nArgs:\n    user_id: ID of the banned user\n    reason: Reason for the appeal\n\nReturns:\n    Tuple of (success, message, appeal_id)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_blocked_user_appeal": {"name": "create_blocked_user_appeal", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create an appeal for a blocked user.\n\nArgs:\n    user_id: ID of the user creating the appeal\n    reason: Reason for the appeal\n\nReturns:\n    Tuple of (success, message, appeal_id)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_journey_appeal": {"name": "create_journey_appeal", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create an appeal for a hidden journey.\n\nArgs:\n    user_id: ID of the user creating the appeal\n    journey_id: ID of the hidden journey\n    reason: Reason for the appeal\n\nReturns:\n    Tuple of (success, message, appeal_id)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "drop_ticket": {"name": "drop_ticket", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": "Allow staff to drop an assigned ticket back to the queue", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_active_tickets": {"name": "get_active_tickets", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get active tickets (open and stalled status)", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_active_tickets_count": {"name": "get_active_tickets_count", "parameters": {"search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get count of active tickets", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_assigned_tickets": {"name": "get_assigned_tickets", "parameters": {"staff_id": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get tickets assigned to a specific staff member (excluding completed tickets)", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_assigned_tickets_count": {"name": "get_assigned_tickets_count", "parameters": {"staff_id": {"type": "any", "default": null, "required": true}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get count of tickets assigned to a specific staff member (excluding completed tickets)", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_journey_appeal_status": {"name": "get_journey_appeal_status", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "journey_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get the latest appeal status for a journey by a user.\n\nArgs:\n    user_id: ID of the user who made the appeal\n    journey_id: ID of the journey being appealed\n\nReturns:\n    Dict with appeal status information or None if no appeal found", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_my_tickets": {"name": "get_my_tickets", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_my_tickets_count": {"name": "get_my_tickets_count", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_past_tickets": {"name": "get_past_tickets", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get past tickets (resolved, approved, rejected)", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_past_tickets_count": {"name": "get_past_tickets_count", "parameters": {"search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get count of past tickets", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_request_type_enum_values": {"name": "get_request_type_enum_values", "parameters": {}, "return_annotation": "<class 'list'>", "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_tickets": {"name": "get_tickets", "parameters": {"user_id": {"type": "any", "default": "None", "required": false}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get public journeys with optional user filter, pagination and search", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_tickets_count": {"name": "get_tickets_count", "parameters": {"user_id": {"type": "any", "default": "None", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_unassigned_tickets": {"name": "get_unassigned_tickets", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get new tickets (status = 'new')", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_unassigned_tickets_count": {"name": "get_unassigned_tickets_count", "parameters": {"search": {"type": "any", "default": "", "required": false}, "status_filter": {"type": "any", "default": "None", "required": false}, "type_filter": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": "Get count of new tickets", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "process_ban_appeal": {"name": "process_ban_appeal", "parameters": {"appeal_id": {"type": "<class 'int'>", "default": null, "required": true}, "staff_id": {"type": "<class 'int'>", "default": null, "required": true}, "action": {"type": "<class 'str'>", "default": null, "required": true}, "response": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Process a ban appeal (approve or reject).\n\nArgs:\n    appeal_id: ID of the appeal\n    staff_id: ID of the staff member processing\n    action: 'approve' or 'reject'\n    response: Optional response message\n\nReturns:\n    Tuple of (success, message)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "process_blocked_user_appeal": {"name": "process_blocked_user_appeal", "parameters": {"appeal_id": {"type": "<class 'int'>", "default": null, "required": true}, "staff_id": {"type": "<class 'int'>", "default": null, "required": true}, "action": {"type": "<class 'str'>", "default": null, "required": true}, "response": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Process a blocked user appeal (approve or reject).\n\nArgs:\n    appeal_id: ID of the appeal\n    staff_id: ID of the staff member processing\n    action: 'approve' or 'reject'\n    response: Optional response message\n\nReturns:\n    Tuple of (success, message)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "process_journey_appeal": {"name": "process_journey_appeal", "parameters": {"appeal_id": {"type": "<class 'int'>", "default": null, "required": true}, "staff_id": {"type": "<class 'int'>", "default": null, "required": true}, "action": {"type": "<class 'str'>", "default": null, "required": true}, "response": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Process a journey appeal (approve or reject).\n\nArgs:\n    appeal_id: ID of the appeal\n    staff_id: ID of the staff member processing\n    action: 'approve' or 'reject'\n    response: Optional response message\n\nReturns:\n    Tuple of (success, message)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "query_staff": {"name": "query_staff", "parameters": {}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "query_ticket_by_id": {"name": "query_ticket_by_id", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "take_ticket": {"name": "take_ticket", "parameters": {"ticket_id": {"type": "any", "default": null, "required": true}, "staff_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": "Allow staff to take an unassigned ticket", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "journey_service": {"functions": {"count_journey_events": {"name": "count_journey_events", "parameters": {"journey_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": "Count events in a journey", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "create_journey": {"name": "create_journey", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "title": {"type": "<class 'str'>", "default": null, "required": true}, "description": {"type": "<class 'str'>", "default": null, "required": true}, "start_date": {"type": "<class 'datetime.date'>", "default": null, "required": true}, "visibility": {"type": "<class 'str'>", "default": "private", "required": false}, "cover_image": {"type": "typing.Optional[werkzeug.datastructures.file_storage.FileStorage]", "default": "None", "required": false}, "no_edits": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a new journey.\n\nArgs:\n    user_id: ID of the user creating the journey.\n    title: Journey title.\n    description: Journey description.\n    start_date: Journey start date.\n    visibility: Journey visibility ('private', 'public', 'published').\n    cover_image: Optional cover image file.\n    no_edits: Whether the journey should be protected from edits by staff.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and journey ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "delete_journey": {"name": "delete_journey", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Delete a journey.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the deletion.\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_all_journeys": {"name": "get_all_journeys", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get all journeys with pagination and search (admin only)", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_all_journeys_count": {"name": "get_all_journeys_count", "parameters": {"search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get total count of all journeys with search (admin only)", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_hidden_journeys": {"name": "get_hidden_journeys", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get hidden journeys with pagination and search (admin only)", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_hidden_journeys_count": {"name": "get_hidden_journeys_count", "parameters": {"search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get total count of hidden journeys with search (admin only)", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_journey": {"name": "get_journey", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "typing.Optional[int]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[typing.Dict[str, typing.Any]]]", "docstring": "Get a journey by ID.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user requesting the journey (for permission check).\n\nReturns:\n    Tuple[bool, str, Optional[Dict[str, Any]]]: Success flag, message, and journey data if found.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_private_journeys": {"name": "get_private_journeys", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get user's private journeys with pagination and search", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_private_journeys_count": {"name": "get_private_journeys_count", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get total count of user's private journeys with search", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_public_journey_count": {"name": "get_public_journey_count", "parameters": {}, "return_annotation": null, "docstring": "Returns the total count of public journeys.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_public_journeys": {"name": "get_public_journeys", "parameters": {"user_id": {"type": "any", "default": "None", "required": false}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get public journeys with optional user filter, pagination and search", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_public_journeys_count": {"name": "get_public_journeys_count", "parameters": {"search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": "Get total count of public journeys with search", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_published_journey": {"name": "get_published_journey", "parameters": {"journey_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_published_journey_count": {"name": "get_published_journey_count", "parameters": {}, "return_annotation": null, "docstring": "Returns the total count of published journeys.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_published_journeys": {"name": "get_published_journeys", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "organize_journeys_by_user": {"name": "organize_journeys_by_user", "parameters": {"journeys": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": "Organize journeys by user for display in hidden journeys pages.\n\nArgs:\n    journeys (list): List of journey dictionaries with user information\n\nReturns:\n    dict: Dictionary mapping user IDs to user info and their journeys", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "remove_journey_cover_image": {"name": "remove_journey_cover_image", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "edit_reason": {"type": "<class 'str'>", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Remove a journey's cover image.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    edit_reason: Reason for the edit (required for staff edits).\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "search_journeys": {"name": "search_journeys", "parameters": {"search_term": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "50", "required": false}, "offset": {"type": "any", "default": "0", "required": false}}, "return_annotation": null, "docstring": "Search public journeys", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "search_journeys_by_location": {"name": "search_journeys_by_location", "parameters": {"location_id": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "50", "required": false}, "offset": {"type": "any", "default": "0", "required": false}}, "return_annotation": null, "docstring": "Search journeys by location", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "search_my_journeys": {"name": "search_my_journeys", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "search_term": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "50", "required": false}, "offset": {"type": "any", "default": "0", "required": false}}, "return_annotation": null, "docstring": "Search user's own journeys", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "update_journey": {"name": "update_journey", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "title": {"type": "typing.Optional[str]", "default": "None", "required": false}, "description": {"type": "typing.Optional[str]", "default": "None", "required": false}, "start_date": {"type": "typing.Optional[datetime.date]", "default": "None", "required": false}, "visibility": {"type": "typing.Optional[str]", "default": "None", "required": false}, "cover_image": {"type": "typing.Optional[werkzeug.datastructures.file_storage.FileStorage]", "default": "None", "required": false}, "no_edits": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "edit_reason": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a journey.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    title: New title (optional).\n    description: New description (optional).\n    start_date: New start date (optional).\n    visibility: New visibility setting ('private', 'public', 'published').\n    cover_image: New cover image (optional).\n    no_edits: Flag to prevent editors from editing the journey.\n    edit_reason: Reason for the edit (required for staff edits)\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_journey_cover_image": {"name": "update_journey_cover_image", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "cover_image": {"type": "<class 'werkzeug.datastructures.file_storage.FileStorage'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a journey's cover image.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    cover_image: New cover image file.\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_journey_hidden_status": {"name": "update_journey_hidden_status", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "is_hidden": {"type": "<class 'bool'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a journey's hidden status (editor/admin only).\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    is_hidden: Whether the journey should be hidden.\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_journey_no_edits": {"name": "update_journey_no_edits", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "no_edits": {"type": "<class 'bool'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a journey's no_edits flag (owner or premium users only).\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    no_edits: Whether the journey should be protected from edits.\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_journey_visibility": {"name": "update_journey_visibility", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "visibility": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a journey's visibility setting.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user making the update.\n    visibility: Journey visibility ('private', 'public', 'published').\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "upload_journey_cover": {"name": "upload_journey_cover", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "file": {"type": "<class 'werkzeug.datastructures.file_storage.FileStorage'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[str]]", "docstring": "Upload a cover photo for a journey.\n\nArgs:\n    journey_id: ID of the journey.\n    user_id: ID of the user uploading the cover.\n    file: Cover image file.\n\nReturns:\n    Tuple[bool, str, Optional[str]]: Success flag, message, and filename.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "location_service": {"functions": {"count_search_results": {"name": "count_search_results", "parameters": {"search_term": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Get total count of locations matching a search term.\n\nArgs:\n    search_term: Search term to match.\n    \nReturns:\n    int: Total number of matching locations.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "create_location": {"name": "create_location", "parameters": {"name": {"type": "<class 'str'>", "default": null, "required": true}, "longitude": {"type": "<class 'float'>", "default": null, "required": true}, "latitude": {"type": "<class 'float'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a new location.\n\nArgs:\n    name: Name of the location.\n    \nReturns:\n    Tuple[bool, str, Optional[int]]: Tuple containing success flag, message, and location ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_location": {"name": "get_location", "parameters": {"location_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a location by ID.\n\nArgs:\n    location_id: ID of the location.\n    \nReturns:\n    Dict[str, Any]: Location object or None if not found.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_location_by_name": {"name": "get_location_by_name", "parameters": {"name": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get location details by it's name\n\nArgs:\n    name: name of the location\n\nReturns:\n    Optional[Dict[str, Any]]]: Location object or None if error occurs", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_locations": {"name": "get_locations", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all locations with pagination and usage count.\n\nArgs:\n    limit: Maximum number of results.\n    offset: Number of results to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of location objects with event count.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_locations_count": {"name": "get_locations_count", "parameters": {}, "return_annotation": "<class 'int'>", "docstring": "Get total number of locations.\n\nReturns:\n    int: Total number of locations in the system.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_locations_for_journey": {"name": "get_locations_for_journey", "parameters": {"journey_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.List[typing.Dict[str, typing.Any]]]", "docstring": "Get all locations associated with events in a specific journey.\n\nArgs:\n    journey_id: ID of the journey.\n\nReturns:\n    List[Dict[str, Any]]: List of location objects, or None if an error occurs.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "search_locations": {"name": "search_locations", "parameters": {"search_term": {"type": "<class 'str'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Search for locations by name with pagination and usage count.\n\nArgs:\n    search_term: Search term to match against location names.\n    limit: Maximum number of results.\n    offset: Number of results to skip.\n    \nReturns:\n    List[Dict[str, Any]]: List of matching location objects with event count.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "update_location": {"name": "update_location", "parameters": {"location_id": {"type": "<class 'int'>", "default": null, "required": true}, "name": {"type": "<class 'str'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "edit_reason": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a location's name.\n\nArgs:\n    location_id: ID of the location.\n    name: New name for the location.\n    user_id: ID of the user making the update.\n    edit_reason: Reason for the edit (required for staff).\n    \nReturns:\n    Tuple[bool, str]: Tuple containing success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "message_service": {"functions": {"check_can_message": {"name": "check_can_message", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "recipient_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Check if a user can send messages to another user.\n\nArgs:\n    user_id (int): The ID of the sender.\n    recipient_id (int): The ID of the recipient.\n\nReturns:\n    Tuple[bool, str]: (Can send, reason if cannot)", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "delete_message": {"name": "delete_message", "parameters": {"message_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Delete a message sent by the current user.\n\nArgs:\n    message_id (int): The ID of the message to delete.\n    user_id (int): The ID of the user (must be the sender).\n\nReturns:\n    bool: True if the message was deleted, False otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_conversation_between": {"name": "get_conversation_between", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "other_user_id": {"type": "any", "default": null, "required": true}}, "return_annotation": null, "docstring": "Check if a conversation exists between two users.\n\nArgs:\n    user_id: ID of the current user\n    other_user_id: ID of the other user\n\nReturns:\n    Dictionary with conversation info if exists, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_conversations": {"name": "get_conversations", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all conversations for a user.\n\nArgs:\n    user_id (int): The ID of the user.\n\nReturns:\n    List[Dict[str, Any]]: A list of conversation data.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_messages_between": {"name": "get_messages_between", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "other_user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get messages exchanged between two users.\n\nArgs:\n    user_id (int): The ID of the current user.\n    other_user_id (int): The ID of the other user.\n    limit (int): Maximum number of messages to return.\n\nReturns:\n    List[Dict[str, Any]]: A list of message records.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_unread_messages_count": {"name": "get_unread_messages_count", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Get the count of unread messages for a user.\n\nArgs:\n    user_id (int): The ID of the user.\n\nReturns:\n    int: The number of unread messages.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_user_by_id": {"name": "get_user_by_id", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get user information by ID.\n\nArgs:\n    user_id (int): The ID of the user.\n\nReturns:\n    Optional[Dict[str, Any]]: The user data or None if not found.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "mark_message_as_read": {"name": "mark_message_as_read", "parameters": {"message_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Mark a message as read by the recipient.\n\nArgs:\n    message_id (int): The ID of the message to mark as read.\n    user_id (int): The ID of the user (must be the recipient).\n\nReturns:\n    bool: True if the message was marked as read, False otherwise.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "send_message": {"name": "send_message", "parameters": {"sender_id": {"type": "<class 'int'>", "default": null, "required": true}, "recipient_id": {"type": "<class 'int'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Send a new private message.\n\nArgs:\n    sender_id (int): The ID of the sender.\n    recipient_id (int): The ID of the recipient.\n    content (str): The message content.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success status, message, and message ID if successful", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": false}}}, "notification_service": {"functions": {"count_unread_notifications": {"name": "count_unread_notifications", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'int'>", "docstring": "Count unread notifications for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    int: Number of unread notifications.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "count_user_notifications": {"name": "count_user_notifications", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "include_read": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "<class 'int'>", "docstring": "Count all notifications for a user.\n\nArgs:\n    user_id: ID of the user.\n    include_read: Whether to include read notifications.\n    \nReturns:\n    int: Total number of notifications.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "create_notification": {"name": "create_notification", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "notification_type": {"type": "<class 'str'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}, "related_id": {"type": "typing.Optional[int]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a notification for a user.\n\nArgs:\n    user_id: ID of the user to notify.\n    notification_type: Type of notification ('edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'achievement', 'report').\n    content: Notification text.\n    related_id: Optional ID related to the notification (e.g., comment ID for comment notifications).\n    \nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and notification ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_user_notifications": {"name": "get_user_notifications", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}, "include_read": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get notifications for a user.\n\nArgs:\n    user_id: ID of the user.\n    limit: Maximum number of notifications to return.\n    offset: Number of notifications to skip.\n    include_read: Whether to include read notifications.\n    \nReturns:\n    List[Dict[str, Any]]: List of notification records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "mark_all_as_read": {"name": "mark_all_as_read", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, int]", "docstring": "Mark all notifications as read for a user.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    Tuple[bool, str, int]: Success flag, message, and number of notifications marked as read.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "mark_all_notifications_as_read": {"name": "mark_all_notifications_as_read", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Mark all notifications for a user as read.\n\nArgs:\n    user_id: ID of the user.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "mark_notification_as_read": {"name": "mark_notification_as_read", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "notification_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Mark a notification as read.\n\nArgs:\n    user_id: ID of the user.\n    notification_id: ID of the notification.\n    \nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "notification_exists": {"name": "notification_exists", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "notification_type": {"type": "<class 'str'>", "default": null, "required": true}, "content": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a notification with the same type and content already exists for the user (read or unread).", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}}}, "report_service": {"functions": {"escalate_comment_to_admin": {"name": "escalate_comment_to_admin", "parameters": {"report_id": {"type": "<class 'int'>", "default": null, "required": true}, "escalate": {"type": "<class 'int'>", "default": null, "required": true}, "escalated_by_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Escalate a comment to admin review by setting escalated_to_admin = 1.\n\nArgs:\n    report_id: ID of the report associated with the comment.\n    escalate: boolean of escalation status\n    escalated_by_admin: ID of staff member who escalated the comment report to admin\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_comment_report": {"name": "get_comment_report", "parameters": {"report_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a comment report by ID.\n\nArgs:\n    report_id: ID of the report.\n\nReturns:\n    Dict[str, Any]: Report record if found, None otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_comment_reports": {"name": "get_comment_reports", "parameters": {"limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "active": {"type": "any", "default": "None", "required": false}, "escalated": {"type": "any", "default": "None", "required": false}, "resolved": {"type": "any", "default": "None", "required": false}, "new": {"type": "any", "default": "None", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get filtered and paginated comment reports.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_comment_reports_count": {"name": "get_comment_reports_count", "parameters": {"search": {"type": "any", "default": "", "required": false}, "active": {"type": "any", "default": "None", "required": false}, "escalated": {"type": "any", "default": "None", "required": false}, "resolved": {"type": "any", "default": "None", "required": false}}, "return_annotation": "<class 'int'>", "docstring": "Returns count of comment reports with optional search and filter.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_my_comment_reports": {"name": "get_my_comment_reports", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "limit": {"type": "any", "default": "10", "required": false}, "offset": {"type": "any", "default": "0", "required": false}, "search": {"type": "any", "default": "", "required": false}, "active": {"type": "any", "default": "None", "required": false}, "resolved": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_my_comment_reports_count": {"name": "get_my_comment_reports_count", "parameters": {"user_id": {"type": "any", "default": null, "required": true}, "active": {"type": "any", "default": "None", "required": false}, "resolved": {"type": "any", "default": "None", "required": false}}, "return_annotation": null, "docstring": null, "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_report": {"name": "get_report", "parameters": {"report_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a report by ID.\n\nArgs:\n    report_id: ID of the report.\n\nReturns:\n    Dict[str, Any]: Report record if found, None otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_reports_by_content": {"name": "get_reports_by_content", "parameters": {"content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all reports for a specific content item.\n\nArgs:\n    content_type: Type of content.\n    content_id: ID of the content.\n\nReturns:\n    List[Dict[str, Any]]: List of report records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "has_user_flagged_comment": {"name": "has_user_flagged_comment", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "comment_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if the user has already flagged a specific comment.\n\nArgs:\n    user_id: ID of the user.\n    comment_id: ID of the comment.\n\nReturns:\n    bool: True if the user has flagged the comment, False otherwise.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "report_content": {"name": "report_content", "parameters": {"reporter_id": {"type": "<class 'int'>", "default": null, "required": true}, "content_type": {"type": "<class 'str'>", "default": null, "required": true}, "content_id": {"type": "<class 'int'>", "default": null, "required": true}, "reason": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Report a content item for moderation.\n\nArgs:\n    reporter_id: ID of the user reporting the content.\n    content_type: Type of content ('comment', 'event', 'journey', 'image', 'user', 'location').\n    content_id: ID of the content.\n    reason: Reason for the report.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and report ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "review_report": {"name": "review_report", "parameters": {"staff_id": {"type": "<class 'int'>", "default": null, "required": true}, "report_id": {"type": "<class 'int'>", "default": null, "required": true}, "status": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Review a report and update its status.\n\nArgs:\n    staff_id: ID of the staff member reviewing the report.\n    report_id: ID of the report.\n    status: New status ('reviewed', 'dismissed', 'resolved', 'open').\n\nReturns:\n    Tuple[bool, str]: Success flag and message.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}, "subscription_service": {"functions": {"calculate_subscription_price": {"name": "calculate_subscription_price", "parameters": {"plan_code": {"type": "<class 'str'>", "default": null, "required": true}, "country_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Dict[str, float]", "docstring": "Calculate subscription price including GST.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "check_can_use_premium_features": {"name": "check_can_use_premium_features", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user can use premium features.\n\nArgs:\n    user_id: ID of the user.\n\nReturns:\n    bool: Whether the user can use premium features.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "check_user_ever_had_premium": {"name": "check_user_ever_had_premium", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user ever had premium subscription (including expired).\n\nThis is used to determine if users can maintain existing no_edits flags\neven after their subscription expires.\n\nArgs:\n    user_id: ID of the user.\n\nReturns:\n    bool: Whether the user ever had premium subscription.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "check_user_had_free_trial": {"name": "check_user_had_free_trial", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user has had a free trial.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "check_user_had_premium": {"name": "check_user_had_premium", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user has had a premium subscription.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "create_free_trial": {"name": "create_free_trial", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a free trial subscription for a user, and also create a payment record for history/receipt.", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_gift_subscription": {"name": "create_gift_subscription", "parameters": {"admin_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "months": {"type": "<class 'int'>", "default": null, "required": true}, "reason": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Gift a subscription to a user (admin only).\n\nArgs:\n    admin_id: ID of the admin giving the gift.\n    user_id: ID of the user receiving the gift.\n    months: Number of months (1-12).\n    reason: Optional reason for gifting.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and subscription ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "create_paid_subscription": {"name": "create_paid_subscription", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "plan_code": {"type": "<class 'str'>", "default": null, "required": true}, "amount": {"type": "<class 'float'>", "default": null, "required": true}, "currency": {"type": "<class 'str'>", "default": null, "required": true}, "country_id": {"type": "<class 'int'>", "default": null, "required": true}, "billing_address": {"type": "<class 'str'>", "default": null, "required": true}, "card_last_four": {"type": "<class 'str'>", "default": null, "required": true}, "transaction_reference": {"type": "<class 'str'>", "default": null, "required": true}, "gst_amount": {"type": "typing.Optional[float]", "default": "None", "required": false}, "reason": {"type": "typing.Optional[str]", "default": "None", "required": false}}, "return_annotation": "typing.Tuple[bool, str, typing.Optional[int]]", "docstring": "Create a paid subscription for a user.\n\nArgs:\n    user_id: ID of the user.\n    plan_code: Code of the subscription plan.\n    amount: Payment amount.\n    currency: Payment currency.\n    country_id: ID of the billing country.\n    billing_address: Billing address.\n    card_last_four: Last four digits of credit card.\n    transaction_reference: Payment transaction reference.\n    gst_amount: Optional GST amount (for applicable countries).\n    reason: Optional reason for the subscription.\n\nReturns:\n    Tuple[bool, str, Optional[int]]: Success flag, message, and subscription ID if created.", "source_available": true, "validates_input": true, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "get_countries": {"name": "get_countries", "parameters": {}, "return_annotation": "<class 'list'>", "docstring": "Return a list of all countries (id, name, currency_code) for payment modal country dropdown.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_payment_by_id": {"name": "get_payment_by_id", "parameters": {"payment_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get payment details by ID, including country info.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_payment_by_subscription_id": {"name": "get_payment_by_subscription_id", "parameters": {"subscription_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get payment details by subscription ID.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_selectable_plans": {"name": "get_selectable_plans", "parameters": {}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Return only premium and is_selectable subscription plans (from data layer).", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_subscription_end_date": {"name": "get_subscription_end_date", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'datetime.date'>", "docstring": "Get the end date of the user's subscription.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_subscription_plan": {"name": "get_subscription_plan", "parameters": {"plan_code": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get subscription plan details by plan code.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_user_active_subscription": {"name": "get_user_active_subscription", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get the active subscription for a user, including plan info from the view.", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_payments": {"name": "get_user_payments", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all payments made by a user.\n\nArgs:\n    user_id: ID of the user.\n\nReturns:\n    List[Dict[str, Any]]: List of payment records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_subscription": {"name": "get_user_subscription", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Dict[str, typing.Any]", "docstring": "Get all relevant subscription info for a user (active, all, payments).", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": false}, "get_user_subscription_history": {"name": "get_user_subscription_history", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": null, "required": true}, "offset": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[typing.List[typing.Dict[str, typing.Any]], int]", "docstring": "Get paginated subscription history with payment information for a user.\n\nArgs:\n    user_id: The user ID\n    limit: Number of records per page\n    offset: Offset for pagination\n\nReturns:\n    Tuple[List[Dict[str, Any]], int]: List of subscription records and total count", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": false}, "get_user_subscription_status": {"name": "get_user_subscription_status", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Dict[str, typing.Any]", "docstring": "Get a user's subscription status.\n\nArgs:\n    user_id: ID of the user.\n\nReturns:\n    Dict[str, Any]: Dictionary with subscription status information.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_subscription_status_for_login": {"name": "get_user_subscription_status_for_login", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Dict[str, typing.Any]", "docstring": "Get a user's subscription status, including most recent expired premium/trial if no active.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_users_expiring_soon": {"name": "get_users_expiring_soon", "parameters": {"days": {"type": "<class 'int'>", "default": "7", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get users whose subscriptions are expiring soon.\n\nArgs:\n    days: Number of days to consider as 'expiring soon'.\n\nReturns:\n    List[Dict[str, Any]]: List of user records with expiration dates.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}}}, "user_service": {"functions": {"count_search_results": {"name": "count_search_results", "parameters": {"search_term": {"type": "<class 'str'>", "default": null, "required": true}, "filter_role": {"type": "typing.Optional[typing.List[str]]", "default": "None", "required": false}, "filter_blocked": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "filter_banned": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "public_only": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "<class 'int'>", "docstring": "Count users matching a search term.\n\nArgs:\n    search_term: Search term to match\n    filter_role: Optional list of roles to filter by\n    filter_blocked: Optional filter for blocked status\n    filter_banned: Optional filter for banned status\n    public_only: If True, only count users who have set their profile as public\n\nReturns:\n    Number of matching users", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_banned_users": {"name": "get_banned_users", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all banned users.\n\nArgs:\n    limit: Maximum number of results\n    offset: Number of results to skip\n\nReturns:\n    List of banned user objects", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_banned_users_count": {"name": "get_banned_users_count", "parameters": {}, "return_annotation": "<class 'int'>", "docstring": "Get count of banned users.\n\nReturns:\n    Number of banned users", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_blocked_users": {"name": "get_blocked_users", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all blocked users.\n\nArgs:\n    limit: Maximum number of results\n    offset: Number of results to skip\n\nReturns:\n    List of blocked user objects", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_blocked_users_count": {"name": "get_blocked_users_count", "parameters": {}, "return_annotation": "<class 'int'>", "docstring": "Get count of blocked users.\n\nReturns:\n    Number of blocked users", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_staff_accounts": {"name": "get_staff_accounts", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all staff accounts (editors, moderators, support_techs, and admins).\n\nArgs:\n    limit: Maximum number of results\n    offset: Number of results to skip\n\nReturns:\n    List of staff account objects", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_staff_count": {"name": "get_staff_count", "parameters": {}, "return_annotation": "<class 'int'>", "docstring": "Get count of staff accounts.\n\nReturns:\n    Number of staff accounts", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_travellers": {"name": "get_travellers", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}, "public_only": {"type": "<class 'bool'>", "default": "False", "required": false}, "exclude_user_id": {"type": "<class 'int'>", "default": "None", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all travellers with pagination.\n\nArgs:\n    limit: Maximum number of results to return. \n    offset: Number of results to skip.\n    exclude_user_id: ID of the user to exclude from the results\n\nReturns:\n    List[Dict[str, Any]]: List of traveller records.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_travellers_count": {"name": "get_travellers_count", "parameters": {"public_only": {"type": "<class 'bool'>", "default": "False", "required": false}, "exclude_user_id": {"type": "<class 'int'>", "default": "None", "required": false}}, "return_annotation": "<class 'int'>", "docstring": "Get count of travellers.\n\nReturns:\n    int: Count of travellers.", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_by_id": {"name": "get_user_by_id", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a user by their ID.\n\nArgs:\n    user_id: ID of the user to retrieve\n\nReturns:\n    Optional[Dict[str, Any]]: User data dictionary if found, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_user_with_subscription_status": {"name": "get_user_with_subscription_status", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "typing.Optional[typing.Dict[str, typing.Any]]", "docstring": "Get a user's basic info and subscription status together.\nArgs:\n    user_id: ID of the user to retrieve\nReturns:\n    Optional[Dict[str, Any]]: User data dictionary with subscription_status if found, None otherwise", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_users": {"name": "get_users", "parameters": {"limit": {"type": "<class 'int'>", "default": "100", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}, "public_only": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Get all users with pagination.\n\nArgs:\n    limit: Maximum number of results\n    offset: Number of results to skip\n    public_only: If True, only return users who have set their profile as public\n\nReturns:\n    List of user objects", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "get_users_count": {"name": "get_users_count", "parameters": {"public_only": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "<class 'int'>", "docstring": "Get total number of users.\n\nArgs:\n    public_only: If True, only count users who have set their profile as public\n\nReturns:\n    Total number of users in the system", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "is_user_blocked": {"name": "is_user_blocked", "parameters": {"user_id": {"type": "<class 'int'>", "default": null, "required": true}}, "return_annotation": "<class 'bool'>", "docstring": "Check if a user is blocked.\n\nArgs:\n    user_id: ID of the user to check\n\nReturns:\n    Whether the user is blocked", "source_available": true, "validates_input": false, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "search_users": {"name": "search_users", "parameters": {"search_term": {"type": "<class 'str'>", "default": null, "required": true}, "limit": {"type": "<class 'int'>", "default": "50", "required": false}, "offset": {"type": "<class 'int'>", "default": "0", "required": false}, "filter_role": {"type": "typing.Optional[typing.List[str]]", "default": "None", "required": false}, "filter_blocked": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "filter_banned": {"type": "typing.Optional[bool]", "default": "None", "required": false}, "public_only": {"type": "<class 'bool'>", "default": "False", "required": false}}, "return_annotation": "typing.List[typing.Dict[str, typing.Any]]", "docstring": "Search for users.\n\nArgs:\n    search_term: Search term to match against user information\n    limit: Maximum number of results\n    offset: Number of results to skip\n    filter_role: Optional list of roles to filter by\n    filter_blocked: Optional filter for blocked status\n    filter_banned: Optional filter for banned status\n    public_only: If True, only return users who have set their profile as public\n\nReturns:\n    List of matching user objects", "source_available": true, "validates_input": true, "returns_tuple": false, "database_calls": 0, "error_handling": true}, "update_user_ban_status": {"name": "update_user_ban_status", "parameters": {"admin_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "is_banned": {"type": "<class 'bool'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a user's ban status (admin or support_tech only).\n\nArgs:\n    admin_id: ID of the admin or support tech making the change\n    user_id: ID of the user to update\n    is_banned: Whether the user should be banned\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_user_block_status": {"name": "update_user_block_status", "parameters": {"admin_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "is_blocked": {"type": "<class 'bool'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a user's block status (admin or support_tech only).\n\nArgs:\n    admin_id: ID of the admin or support tech making the change\n    user_id: ID of the user to update\n    is_blocked: Whether the user should be blocked\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}, "update_user_role": {"name": "update_user_role", "parameters": {"admin_id": {"type": "<class 'int'>", "default": null, "required": true}, "user_id": {"type": "<class 'int'>", "default": null, "required": true}, "role": {"type": "<class 'str'>", "default": null, "required": true}}, "return_annotation": "typing.Tuple[bool, str]", "docstring": "Update a user's role (admin only).\n\nArgs:\n    admin_id: ID of the admin making the change\n    user_id: ID of the user to update\n    role: New role ('traveller', 'editor', 'admin', 'moderator', 'support_tech')\n\nReturns:\n    A tuple containing:\n        - bool: Success status\n        - str: Message describing the result", "source_available": true, "validates_input": false, "returns_tuple": true, "database_calls": 0, "error_handling": true}}}}