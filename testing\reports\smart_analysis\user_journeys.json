[{"name": "Complete Authentication Journey", "description": "User registration, login, and session management", "test_cases": [{"name": "User Registration", "function": "register_user", "test_data": {"username": "testuser", "email": "<EMAIL>", "password": "testpass123", "confirm_password": "testpass123", "first_name": "test_first_name", "last_name": "test_last_name", "location": "Test Location"}, "expected_success": true, "validates": ["user_created", "password_hashed", "unique_username"]}, {"name": "User Authentication", "function": "authenticate_user", "test_data": {"username": "testuser", "password": "testpass123"}, "expected_success": true, "validates": ["credentials_verified", "user_data_returned"]}]}, {"name": "Journey Management Lifecycle", "description": "Complete CRUD operations for journeys", "test_cases": [{"name": "Journey Creation", "function": "create_journey", "test_data": {"user_id": 1, "title": "Test Title", "description": "Test description for description", "start_date": "2025-01-01", "visibility": "private", "cover_image": null, "no_edits": "test_no_edits"}, "expected_success": true, "validates": ["journey_created", "user_ownership", "default_visibility"]}, {"name": "Journey Retrieval", "function": "get_journey", "test_data": {"journey_id": 1, "user_id": 1}, "expected_success": true, "validates": ["journey_found", "permission_checked", "data_complete"]}, {"name": "Journey Update", "function": "update_journey", "test_data": {"journey_id": 1, "user_id": 1, "title": "Updated Journey Title", "description": "Test description for description", "start_date": "2025-01-01", "visibility": "private", "cover_image": null, "no_edits": "test_no_edits", "edit_reason": "test_edit_reason"}, "expected_success": true, "validates": ["journey_updated", "changes_saved", "ownership_verified"]}, {"name": "Journey Deletion", "function": "delete_journey", "test_data": {"journey_id": 1, "user_id": 1}, "expected_success": true, "validates": ["journey_deleted", "related_data_handled", "ownership_verified"]}]}, {"name": "Event Management Workflow", "description": "Adding and managing events within journeys", "test_cases": [{"name": "Event Creation", "function": "create_event", "test_data": {"journey_id": 1, "user_id": 1, "location_name": "Test Location", "title": "Test Title", "description": "Test description for description", "start_datetime": "2025-01-01", "end_datetime": "2025-01-02", "images": null, "longitude": -74.006, "latitude": 40.7128}, "expected_success": true, "validates": ["event_created", "linked_to_journey", "location_processed"]}]}]