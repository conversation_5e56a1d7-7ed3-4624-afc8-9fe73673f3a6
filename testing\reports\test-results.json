{"config": {"configFile": "D:\\BoxSync\\2. Univeristy\\5. Lincoln University\\3. MAC\\2. Course\\3. COMP639 Studio Project\\3. Group Assignment 2 -\\source code\\COMP639_Project_2_BHE\\testing\\playwright.config.js", "rootDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "./reports/playwright"}], ["json", {"outputFile": "./reports/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/BoxSync/2. Univeristy/5. Lincoln University/3. MAC/2. Course/3. COMP639 Studio Project/3. Group Assignment 2 -/source code/COMP639_Project_2_BHE/testing/playwright/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 6, "webServer": {"command": "cd .. && python app.py", "url": "http://127.0.0.1:5000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "auth\\example_login.spec.js", "file": "auth/example_login.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "User Authentication", "file": "auth/example_login.spec.js", "line": 4, "column": 6, "specs": [{"title": "login page loads correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-36b9f8c3aac614db25ff", "file": "auth/example_login.spec.js", "line": 6, "column": 3}, {"title": "homepage redirects to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-1507faf1af5c81e43798", "file": "auth/example_login.spec.js", "line": 18, "column": 3}, {"title": "login page loads correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-afefb264cbbf17a53129", "file": "auth/example_login.spec.js", "line": 6, "column": 3}, {"title": "homepage redirects to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-3f12470db2796af73bca", "file": "auth/example_login.spec.js", "line": 18, "column": 3}, {"title": "login page loads correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-e959713b2283093fd86b", "file": "auth/example_login.spec.js", "line": 6, "column": 3}, {"title": "homepage redirects to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-7f48a060e5876b476038", "file": "auth/example_login.spec.js", "line": 18, "column": 3}, {"title": "login page loads correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-999eccef7a9fc36fb242", "file": "auth/example_login.spec.js", "line": 6, "column": 3}, {"title": "homepage redirects to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-a016ff3783f5fff799d3", "file": "auth/example_login.spec.js", "line": 18, "column": 3}, {"title": "login page loads correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-7bd78e708d5682a2e865", "file": "auth/example_login.spec.js", "line": 6, "column": 3}, {"title": "homepage redirects to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d6232e9560f49528f792-3e80b42a5a0817c06617", "file": "auth/example_login.spec.js", "line": 18, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-04T23:27:04.769Z", "duration": 87.53899999999999, "expected": 0, "skipped": 10, "unexpected": 0, "flaky": 0}}