# Python Testing Dependencies for COMP639 Project

# Core testing framework
pytest>=7.4.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-xdist>=3.3.0  # Parallel test execution

# Browser testing (choose one or both)
playwright>=1.52.0    # Modern, fast browser automation
selenium>=4.15.0      # Traditional browser automation
webdriver-manager>=4.0.0  # Automatic driver management for Selenium

# Flask testing utilities
flask-testing>=0.8.1

# MySQL testing support (matching your project's database)
mysql-connector-python>=9.2.0

# Additional testing utilities
coverage>=7.2.0
mock>=5.1.0

# Test data generation and factories
faker>=19.0.0
factory-boy>=3.3.0

# HTTP testing
requests>=2.31.0
responses>=0.23.0

# Async testing support
pytest-asyncio>=0.21.0

# Enhanced test reporting
pytest-html>=3.2.0
allure-pytest>=2.13.0

# Screenshot and video capture
pillow>=10.0.0  # For screenshot processing

# Code quality and formatting
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0

# Environment and configuration
python-dotenv>=1.0.0  # For test environment variables
