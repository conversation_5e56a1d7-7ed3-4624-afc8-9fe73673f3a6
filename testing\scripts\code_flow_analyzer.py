#!/usr/bin/env python3
"""
Code Flow Analyzer for Intelligent Test Generation

This script analyzes your Flask webapp code to understand user workflows
and automatically generates realistic test scenarios based on:
- Route definitions and URL patterns
- Service function signatures and logic
- Database relationships and constraints
- Business logic and validation rules
"""

import ast
import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json

class CodeFlowAnalyzer:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.routes = {}
        self.services = {}
        self.models = {}
        self.workflows = []
        
    def analyze_routes(self):
        """Analyze Flask routes to understand URL patterns and HTTP methods"""
        print("🔍 Analyzing Flask routes...")
        
        routes_dir = self.project_root / "routes"
        if not routes_dir.exists():
            print("❌ Routes directory not found")
            return
        
        for route_file in routes_dir.glob("*.py"):
            self._analyze_route_file(route_file)
    
    def _analyze_route_file(self, file_path):
        """Analyze a single route file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse AST
            tree = ast.parse(content)
            
            # Extract route decorators and functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    route_info = self._extract_route_info(node, content)
                    if route_info:
                        blueprint = file_path.stem.replace('_routes', '')
                        if blueprint not in self.routes:
                            self.routes[blueprint] = []
                        self.routes[blueprint].append(route_info)
                        
        except Exception as e:
            print(f"⚠️  Error analyzing {file_path}: {e}")
    
    def _extract_route_info(self, func_node, content):
        """Extract route information from function decorators"""
        route_info = {
            'function': func_node.name,
            'methods': ['GET'],  # Default
            'url': None,
            'requires_auth': False,
            'parameters': [],
            'form_fields': [],
            'redirects_to': [],
            'renders_template': None
        }
        
        # Check decorators
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Call):
                if hasattr(decorator.func, 'attr') and decorator.func.attr == 'route':
                    # Extract URL pattern
                    if decorator.args:
                        route_info['url'] = ast.literal_eval(decorator.args[0])
                    
                    # Extract methods
                    for keyword in decorator.keywords:
                        if keyword.arg == 'methods':
                            route_info['methods'] = ast.literal_eval(keyword.value)
                
                elif hasattr(decorator.func, 'id') and 'login_required' in decorator.func.id:
                    route_info['requires_auth'] = True
        
        # Extract function parameters
        for arg in func_node.args.args:
            if arg.arg not in ['self']:
                route_info['parameters'].append(arg.arg)
        
        # Analyze function body for patterns
        func_source = ast.get_source_segment(content, func_node)
        if func_source:
            # Look for form field access
            form_fields = re.findall(r"request\.form\[?['\"](\w+)['\"]", func_source)
            route_info['form_fields'].extend(form_fields)
            
            # Look for redirects
            redirects = re.findall(r"redirect\(['\"]([^'\"]+)['\"]", func_source)
            route_info['redirects_to'].extend(redirects)
            
            # Look for template rendering
            template_match = re.search(r"render_template\(['\"]([^'\"]+)['\"]", func_source)
            if template_match:
                route_info['renders_template'] = template_match.group(1)
        
        return route_info if route_info['url'] else None
    
    def analyze_services(self):
        """Analyze service functions to understand business logic"""
        print("🔍 Analyzing service functions...")
        
        services_dir = self.project_root / "services"
        if not services_dir.exists():
            print("❌ Services directory not found")
            return
        
        for service_file in services_dir.glob("*.py"):
            self._analyze_service_file(service_file)
    
    def _analyze_service_file(self, file_path):
        """Analyze a single service file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            service_name = file_path.stem
            self.services[service_name] = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and not node.name.startswith('_'):
                    func_info = self._extract_service_function_info(node, content)
                    self.services[service_name].append(func_info)
                    
        except Exception as e:
            print(f"⚠️  Error analyzing {file_path}: {e}")
    
    def _extract_service_function_info(self, func_node, content):
        """Extract service function information"""
        func_info = {
            'name': func_node.name,
            'parameters': [],
            'returns_tuple': False,
            'validates_input': False,
            'database_operations': [],
            'error_conditions': []
        }
        
        # Extract parameters
        for arg in func_node.args.args:
            func_info['parameters'].append({
                'name': arg.arg,
                'type': 'any'  # Could be enhanced with type hints
            })
        
        # Analyze function body
        func_source = ast.get_source_segment(content, func_node)
        if func_source:
            # Check return pattern
            if 'return (' in func_source or 'return success,' in func_source:
                func_info['returns_tuple'] = True
            
            # Check for validation
            if 'validate' in func_source.lower() or 'if not' in func_source:
                func_info['validates_input'] = True
            
            # Check for database operations
            db_ops = re.findall(r'execute_query\([^)]+\)', func_source)
            func_info['database_operations'] = len(db_ops)
            
            # Check for error conditions
            error_patterns = re.findall(r'return.*False.*["\']([^"\']+)["\']', func_source)
            func_info['error_conditions'].extend(error_patterns)
        
        return func_info
    
    def generate_user_workflows(self):
        """Generate realistic user workflows based on code analysis"""
        print("🎯 Generating user workflows...")
        
        workflows = []
        
        # Authentication workflow
        auth_workflow = self._generate_auth_workflow()
        if auth_workflow:
            workflows.append(auth_workflow)
        
        # Journey management workflow
        journey_workflow = self._generate_journey_workflow()
        if journey_workflow:
            workflows.append(journey_workflow)
        
        # Event management workflow
        event_workflow = self._generate_event_workflow()
        if event_workflow:
            workflows.append(event_workflow)
        
        self.workflows = workflows
        return workflows
    
    def _generate_auth_workflow(self):
        """Generate authentication workflow based on auth routes"""
        if 'auth' not in self.routes:
            return None
        
        auth_routes = self.routes['auth']
        workflow = {
            'name': 'User Authentication Workflow',
            'description': 'Complete user registration and login flow',
            'steps': []
        }
        
        # Find registration route
        register_route = next((r for r in auth_routes if 'register' in r['url']), None)
        if register_route:
            workflow['steps'].append({
                'action': 'GET',
                'url': register_route['url'],
                'description': 'Access registration page',
                'expected_status': 200,
                'validates': ['form fields present', 'CSRF token']
            })
            
            if 'POST' in register_route['methods']:
                workflow['steps'].append({
                    'action': 'POST',
                    'url': register_route['url'],
                    'description': 'Submit registration form',
                    'data': self._generate_form_data(register_route['form_fields']),
                    'expected_status': [200, 302],
                    'validates': ['user created', 'redirect or success message']
                })
        
        # Find login route
        login_route = next((r for r in auth_routes if 'login' in r['url']), None)
        if login_route:
            workflow['steps'].append({
                'action': 'GET',
                'url': login_route['url'],
                'description': 'Access login page',
                'expected_status': 200,
                'validates': ['login form present']
            })
            
            if 'POST' in login_route['methods']:
                workflow['steps'].append({
                    'action': 'POST',
                    'url': login_route['url'],
                    'description': 'Submit login credentials',
                    'data': {
                        'username': 'testuser',
                        'password': 'testpass'
                    },
                    'expected_status': [200, 302],
                    'validates': ['authentication successful', 'session created']
                })
        
        return workflow
    
    def _generate_journey_workflow(self):
        """Generate journey management workflow"""
        if 'journey' not in self.routes:
            return None
        
        journey_routes = self.routes['journey']
        workflow = {
            'name': 'Journey Management Workflow',
            'description': 'Complete journey CRUD operations',
            'requires_auth': True,
            'steps': []
        }
        
        # List journeys
        list_route = next((r for r in journey_routes if r['url'].endswith('/private')), None)
        if list_route:
            workflow['steps'].append({
                'action': 'GET',
                'url': list_route['url'],
                'description': 'View journey list',
                'expected_status': 200,
                'validates': ['journeys displayed', 'create button present']
            })
        
        # Create journey
        create_route = next((r for r in journey_routes if 'new' in r['url']), None)
        if create_route:
            workflow['steps'].extend([
                {
                    'action': 'GET',
                    'url': create_route['url'],
                    'description': 'Access journey creation form',
                    'expected_status': 200,
                    'validates': ['form fields present']
                },
                {
                    'action': 'POST',
                    'url': create_route['url'],
                    'description': 'Create new journey',
                    'data': self._generate_journey_data(),
                    'expected_status': [200, 302],
                    'validates': ['journey created', 'redirect to journey or list']
                }
            ])
        
        return workflow
    
    def _generate_event_workflow(self):
        """Generate event management workflow"""
        if 'event' not in self.routes:
            return None
        
        event_routes = self.routes['event']
        workflow = {
            'name': 'Event Management Workflow',
            'description': 'Add events to journeys',
            'requires_auth': True,
            'depends_on': ['journey_created'],
            'steps': []
        }
        
        # Create event
        create_route = next((r for r in event_routes if 'new' in r['url']), None)
        if create_route:
            workflow['steps'].extend([
                {
                    'action': 'GET',
                    'url': create_route['url'].replace('<int:journey_id>', '1'),
                    'description': 'Access event creation form',
                    'expected_status': 200,
                    'validates': ['event form present', 'location field present']
                },
                {
                    'action': 'POST',
                    'url': create_route['url'].replace('<int:journey_id>', '1'),
                    'description': 'Create new event',
                    'data': self._generate_event_data(),
                    'expected_status': [200, 302],
                    'validates': ['event created', 'added to journey']
                }
            ])
        
        return workflow
    
    def _generate_form_data(self, form_fields):
        """Generate realistic form data based on field names"""
        data = {}
        for field in form_fields:
            if 'username' in field.lower():
                data[field] = 'testuser'
            elif 'email' in field.lower():
                data[field] = '<EMAIL>'
            elif 'password' in field.lower():
                data[field] = 'testpass123'
            elif 'name' in field.lower():
                if 'first' in field.lower():
                    data[field] = 'Test'
                elif 'last' in field.lower():
                    data[field] = 'User'
                else:
                    data[field] = 'Test Name'
            elif 'title' in field.lower():
                data[field] = 'Test Title'
            elif 'description' in field.lower():
                data[field] = 'Test Description'
            else:
                data[field] = f'test_{field}'
        return data
    
    def _generate_journey_data(self):
        """Generate realistic journey data"""
        return {
            'title': 'Automated Test Journey',
            'description': 'Journey created by automated workflow test',
            'start_date': '2025-01-01',
            'visibility': 'private'
        }
    
    def _generate_event_data(self):
        """Generate realistic event data"""
        return {
            'title': 'Automated Test Event',
            'description': 'Event created by automated workflow test',
            'location_name': 'Test Location',
            'start_datetime': '2025-01-01T10:00',
            'end_datetime': '2025-01-01T12:00'
        }
    
    def generate_test_files(self):
        """Generate actual test files based on analyzed workflows"""
        print("📝 Generating test files...")
        
        for workflow in self.workflows:
            self._generate_workflow_test_file(workflow)
    
    def _generate_workflow_test_file(self, workflow):
        """Generate a test file for a specific workflow"""
        filename = f"test_{workflow['name'].lower().replace(' ', '_')}.py"
        filepath = self.project_root / "testing" / "integration_tests" / "generated" / filename
        
        # Create directory if it doesn't exist
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        test_content = self._generate_test_content(workflow)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ Generated: {filepath}")
    
    def _generate_test_content(self, workflow):
        """Generate the actual test code content"""
        content = f'''"""
Generated workflow test: {workflow['name']}

{workflow['description']}

This test was automatically generated by analyzing your codebase structure.
"""

import pytest

@pytest.mark.integration
@pytest.mark.workflow
@pytest.mark.generated
class Test{workflow['name'].replace(' ', '')}:
    """Generated test for {workflow['name']}"""
    
    def test_{workflow['name'].lower().replace(' ', '_')}(self, integration_client):
        """
        {workflow['description']}
        
        This test simulates the complete user workflow by:
'''
        
        for i, step in enumerate(workflow['steps'], 1):
            content += f"        {i}. {step['description']}\n"
        
        content += '        """\n        client = integration_client\n\n'
        
        # Generate authentication if required
        if workflow.get('requires_auth', False):
            content += '''        # Step 0: Authenticate user
        login_response = client.post('/login', data={
            'username': 'testuser',
            'password': 'testpass'
        })
        assert login_response.status_code in [200, 302]
        
'''
        
        # Generate test steps
        for i, step in enumerate(workflow['steps'], 1):
            content += f"        # Step {i}: {step['description']}\n"
            
            if step['action'] == 'GET':
                content += f"        response = client.get('{step['url']}')\n"
            elif step['action'] == 'POST':
                if 'data' in step:
                    data_str = str(step['data']).replace("'", '"')
                    content += f"        response = client.post('{step['url']}', data={data_str})\n"
                else:
                    content += f"        response = client.post('{step['url']}')\n"
            
            # Add assertions
            if isinstance(step['expected_status'], list):
                content += f"        assert response.status_code in {step['expected_status']}\n"
            else:
                content += f"        assert response.status_code == {step['expected_status']}\n"
            
            # Add validation comments
            for validation in step.get('validates', []):
                content += f"        # Validate: {validation}\n"
            
            content += "\n"
        
        return content
    
    def run_analysis(self):
        """Run complete code analysis and generate tests"""
        print("🚀 Starting intelligent code flow analysis...")
        print("=" * 60)
        
        self.analyze_routes()
        self.analyze_services()
        workflows = self.generate_user_workflows()
        
        print(f"\n📊 Analysis Results:")
        print(f"   Routes analyzed: {sum(len(routes) for routes in self.routes.values())}")
        print(f"   Services analyzed: {sum(len(funcs) for funcs in self.services.values())}")
        print(f"   Workflows generated: {len(workflows)}")
        
        # Save analysis results
        self.save_analysis_results()
        
        # Generate test files
        self.generate_test_files()
        
        print("\n🎉 Intelligent test generation completed!")
        return workflows
    
    def save_analysis_results(self):
        """Save analysis results to JSON files"""
        results_dir = self.project_root / "testing" / "reports" / "code_analysis"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Save routes analysis
        with open(results_dir / "routes_analysis.json", 'w') as f:
            json.dump(self.routes, f, indent=2)
        
        # Save services analysis
        with open(results_dir / "services_analysis.json", 'w') as f:
            json.dump(self.services, f, indent=2)
        
        # Save workflows
        with open(results_dir / "generated_workflows.json", 'w') as f:
            json.dump(self.workflows, f, indent=2)
        
        print(f"📄 Analysis results saved to: {results_dir}")

def main():
    analyzer = CodeFlowAnalyzer()
    workflows = analyzer.run_analysis()
    
    print("\n🎯 Generated Workflows:")
    for workflow in workflows:
        print(f"   • {workflow['name']}: {len(workflow['steps'])} steps")

if __name__ == "__main__":
    main()
