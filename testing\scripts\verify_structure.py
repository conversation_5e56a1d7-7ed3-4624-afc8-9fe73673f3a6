#!/usr/bin/env python3
"""
Verification script for testing directory structure

This script verifies that the testing directory follows the documented structure
and all required files are present.
"""

import os
from pathlib import Path
import sys

class TestingStructureVerifier:
    def __init__(self):
        self.testing_root = Path(__file__).parent.parent
        self.errors = []
        self.warnings = []
        self.success_count = 0
        
    def check_file_exists(self, file_path, required=True):
        """Check if a file exists"""
        full_path = self.testing_root / file_path
        if full_path.exists():
            self.success_count += 1
            print(f"✅ {file_path}")
            return True
        else:
            if required:
                self.errors.append(f"❌ Missing required file: {file_path}")
            else:
                self.warnings.append(f"⚠️  Optional file missing: {file_path}")
            return False
    
    def check_directory_exists(self, dir_path, required=True):
        """Check if a directory exists"""
        full_path = self.testing_root / dir_path
        if full_path.exists() and full_path.is_dir():
            self.success_count += 1
            print(f"✅ {dir_path}/")
            return True
        else:
            if required:
                self.errors.append(f"❌ Missing required directory: {dir_path}/")
            else:
                self.warnings.append(f"⚠️  Optional directory missing: {dir_path}/")
            return False
    
    def verify_structure(self):
        """Verify the complete testing directory structure"""
        print("🔍 Verifying Testing Directory Structure")
        print("=" * 50)
        
        # Configuration & Management
        print("\n📋 Configuration & Management:")
        self.check_file_exists("pytest.ini")
        self.check_file_exists("requirements.txt")
        self.check_file_exists("conftest.py")
        self.check_directory_exists("scripts")
        self.check_file_exists("scripts/test_manager.py")
        self.check_file_exists("scripts/setup_testing.py")
        self.check_file_exists("scripts/verify_structure.py")
        
        # Unit Tests
        print("\n🔬 Unit Tests:")
        self.check_directory_exists("unit_tests")
        self.check_file_exists("unit_tests/conftest.py")
        self.check_file_exists("unit_tests/test_basic.py")
        
        # Unit Tests - Services
        self.check_directory_exists("unit_tests/services")
        self.check_file_exists("unit_tests/services/test_journey_service.py")
        
        # Unit Tests - Routes
        self.check_directory_exists("unit_tests/routes")
        self.check_file_exists("unit_tests/routes/test_auth_routes.py")
        self.check_file_exists("unit_tests/routes/test_journey_routes.py")
        
        # Unit Tests - Data
        self.check_directory_exists("unit_tests/data")
        self.check_file_exists("unit_tests/data/test_journey_data.py")
        
        # Unit Tests - Utils
        self.check_directory_exists("unit_tests/utils")
        self.check_file_exists("unit_tests/utils/test_db_utils.py")
        
        # Browser Tests
        print("\n🌐 Browser Tests:")
        self.check_directory_exists("browser_tests")
        self.check_file_exists("browser_tests/conftest.py")
        
        # Browser Tests - Auth
        self.check_directory_exists("browser_tests/auth")
        self.check_file_exists("browser_tests/auth/test_authentication.py")
        
        # Browser Tests - Journeys
        self.check_directory_exists("browser_tests/journeys")
        self.check_file_exists("browser_tests/journeys/test_journey_management.py")
        
        # Browser Tests - Other categories
        self.check_directory_exists("browser_tests/events")
        self.check_directory_exists("browser_tests/admin")
        self.check_directory_exists("browser_tests/utils")
        
        # Integration Tests
        print("\n🔗 Integration Tests:")
        self.check_directory_exists("integration_tests")
        self.check_file_exists("integration_tests/conftest.py")
        
        # Integration Tests - API
        self.check_directory_exists("integration_tests/api")
        self.check_file_exists("integration_tests/api/test_journey_api.py")
        
        # Integration Tests - Database
        self.check_directory_exists("integration_tests/database")
        self.check_file_exists("integration_tests/database/test_database_integration.py")
        
        # Integration Tests - Workflows
        self.check_directory_exists("integration_tests/workflows")
        self.check_file_exists("integration_tests/workflows/test_user_workflows.py")
        
        # Reports & Artifacts
        print("\n📊 Reports & Artifacts:")
        self.check_directory_exists("reports")
        self.check_directory_exists("reports/coverage")
        self.check_directory_exists("reports/html")
        self.check_directory_exists("reports/screenshots")
        self.check_directory_exists("reports/videos")
        
        # Test Data & Fixtures
        print("\n🗂️ Test Data & Fixtures:")
        self.check_directory_exists("fixtures")
        self.check_file_exists("fixtures/test_users.json")
        self.check_file_exists("fixtures/test_journeys.json")
        
        # Documentation
        print("\n📚 Documentation:")
        self.check_directory_exists("docs")
        self.check_file_exists("docs/PYTHON_TESTING_GUIDE.md")
        self.check_file_exists("docs/WEBAPP_STRUCTURE.md")
        self.check_file_exists("docs/COMPREHENSIVE_GUIDE.md")
        
        # Convenience files
        print("\n🛠️ Convenience Files:")
        self.check_file_exists("README.md")
        self.check_file_exists("run_tests.bat", required=False)
        self.check_file_exists("run_tests.sh", required=False)
        
    def check_python_imports(self):
        """Check that key Python modules can be imported"""
        print("\n🐍 Python Import Verification:")
        
        # Add project root to path
        project_root = self.testing_root.parent
        sys.path.insert(0, str(project_root))
        
        try:
            from app import create_app
            print("✅ Flask app import successful")
            self.success_count += 1
        except ImportError as e:
            self.errors.append(f"❌ Flask app import failed: {e}")
        
        try:
            import pytest
            print(f"✅ pytest {pytest.__version__}")
            self.success_count += 1
        except ImportError:
            self.errors.append("❌ pytest not installed")
        
        try:
            import playwright
            print("✅ playwright installed")
            self.success_count += 1
        except ImportError:
            self.warnings.append("⚠️  playwright not installed")

        try:
            import selenium
            print(f"✅ selenium {selenium.__version__}")
            self.success_count += 1
        except ImportError:
            self.warnings.append("⚠️  selenium not installed")
    
    def run_basic_tests(self):
        """Run basic tests to verify functionality"""
        print("\n🧪 Basic Test Verification:")
        
        import subprocess
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                'unit_tests/test_basic.py', 
                '-v', '--tb=short'
            ], cwd=self.testing_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Basic tests passed")
                self.success_count += 1
            else:
                self.errors.append(f"❌ Basic tests failed: {result.stderr}")
                
        except Exception as e:
            self.errors.append(f"❌ Could not run basic tests: {e}")
    
    def print_summary(self):
        """Print verification summary"""
        print("\n" + "=" * 50)
        print("📊 VERIFICATION SUMMARY")
        print("=" * 50)
        
        print(f"✅ Successful checks: {self.success_count}")
        print(f"❌ Errors: {len(self.errors)}")
        print(f"⚠️  Warnings: {len(self.warnings)}")
        
        if self.errors:
            print("\n❌ ERRORS:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print("\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        if not self.errors:
            print("\n🎉 STRUCTURE VERIFICATION PASSED!")
            print("Your testing directory follows the documented structure.")
        else:
            print("\n💥 STRUCTURE VERIFICATION FAILED!")
            print("Please fix the errors above.")
        
        return len(self.errors) == 0

def main():
    verifier = TestingStructureVerifier()
    
    print("🧪 Testing Directory Structure Verification")
    print("=" * 60)
    print("This script verifies that your testing directory follows")
    print("the documented structure and all files are present.")
    print("=" * 60)
    
    # Run verification
    verifier.verify_structure()
    verifier.check_python_imports()
    verifier.run_basic_tests()
    
    # Print summary
    success = verifier.print_summary()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
