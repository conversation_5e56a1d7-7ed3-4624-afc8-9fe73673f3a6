"""
Unit test configuration for Footprints webapp Python testing

This module provides pytest fixtures for testing the Footprints Flask webapp.
Includes fixtures for Flask app, database mocking, service mocking, and test data.
"""

import sys
from pathlib import Path
from datetime import datetime, date
import pytest
from unittest.mock import MagicMock

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app import create_app

@pytest.fixture
def app():
    """
    Flask app instance for testing
    """
    app = create_app()
    app.config.update({
        "TESTING": True,
        "SERVER_NAME": "localhost",
        "WTF_CSRF_ENABLED": False,  # Disable CSRF for testing
    })
    
    with app.app_context():
        yield app

@pytest.fixture
def client(app):
    """
    Flask test client for HTTP requests
    """
    return app.test_client()

@pytest.fixture
def runner(app):
    """
    Flask CLI runner for testing CLI commands
    """
    return app.test_cli_runner()

# Database mocking fixtures
@pytest.fixture
def mock_execute_query(mocker):
    """Mock the execute_query function from db_utils"""
    return mocker.patch('utils.db_utils.execute_query', autospec=True)

@pytest.fixture
def mock_mysql_connection(mocker):
    """Mock MySQL connection"""
    mock_conn = MagicMock()
    mock_cursor = MagicMock()
    mock_conn.cursor.return_value = mock_cursor
    return mocker.patch('utils.db_utils.get_db_connection', return_value=mock_conn)

# Service layer mocking fixtures
@pytest.fixture
def mock_journey_service(mocker):
    """Mock journey service functions"""
    return mocker.patch('services.journey_service')

@pytest.fixture
def mock_event_service(mocker):
    """Mock event service functions"""
    return mocker.patch('services.event_service')

@pytest.fixture
def mock_user_service(mocker):
    """Mock user service functions"""
    return mocker.patch('services.user_service')

@pytest.fixture
def mock_auth_service(mocker):
    """Mock authentication service functions"""
    return mocker.patch('services.auth_service')

@pytest.fixture
def mock_account_service(mocker):
    """Mock account service functions"""
    return mocker.patch('services.account_service')

@pytest.fixture
def mock_location_service(mocker):
    """Mock location service functions"""
    return mocker.patch('services.location_service')

@pytest.fixture
def mock_subscription_service(mocker):
    """Mock subscription service functions"""
    return mocker.patch('services.subscription_service')

# Test data fixtures
@pytest.fixture
def sample_user():
    """Sample user data for testing"""
    return {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "password_hash": "hashed_password",
        "first_name": "Test",
        "last_name": "User",
        "location": "Test City",
        "description": "Test user description",
        "is_blocked": False,
        "is_banned": False,
        "is_public": True,
        "role": "traveller",
        "created_at": datetime.now()
    }

@pytest.fixture
def sample_journey():
    """Sample journey data for testing"""
    return {
        "id": 1,
        "title": "Test Journey",
        "description": "Test Description",
        "start_date": date(2025, 1, 1),
        "visibility": "private",  # Updated to match actual schema
        "cover_image": None,
        "no_edits": False,
        "is_hidden": False,
        "user_id": 1,
        "created_at": datetime.now()
    }

@pytest.fixture
def sample_event():
    """Sample event data for testing"""
    return {
        "id": 1,
        "title": "Test Event",
        "description": "Test Event Description",
        "location_name": "Test Location",
        "start_datetime": datetime(2025, 1, 1, 10, 0),
        "end_datetime": datetime(2025, 1, 1, 12, 0),
        "journey_id": 1,
        "user_id": 1,
        "created_at": datetime.now()
    }

@pytest.fixture
def sample_location():
    """Sample location data for testing"""
    return {
        "id": 1,
        "name": "Test Location",
        "latitude": 40.7128,
        "longitude": -74.0060,
        "created_at": datetime.now()
    }

# Authentication fixtures
@pytest.fixture
def authenticated_session(client, sample_user):
    """Create an authenticated session"""
    with client.session_transaction() as session:
        session['user_id'] = sample_user['id']
        session['username'] = sample_user['username']
    return client

@pytest.fixture
def admin_session(client):
    """Create an admin session"""
    with client.session_transaction() as session:
        session['user_id'] = 999
        session['username'] = 'admin'
        session['is_admin'] = True
    return client
