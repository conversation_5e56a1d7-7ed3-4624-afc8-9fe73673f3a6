"""
Unit tests for journey data layer

Tests the data layer functions that handle direct database operations for journeys.
These tests focus on SQL query construction and database interaction patterns.
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, date

@pytest.mark.unit
@pytest.mark.journey
@pytest.mark.database
class TestJourneyData:
    """Test journey data layer functions"""
    
    def test_create_journey_data(self, app, mock_execute_query):
        """Test journey creation at data layer"""
        with app.app_context():
            # Mock successful journey creation
            mock_execute_query.return_value = 123
            
            try:
                from data.journey_data import create_journey
                
                journey_id = create_journey(
                    user_id=1,
                    title="Test Journey",
                    description="Test Description",
                    start_date=date(2025, 1, 1),
                    visibility="private"
                )
                
                assert journey_id == 123
                mock_execute_query.assert_called_once()
                
                # Verify SQL query structure
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "INSERT INTO journeys" in query
                assert "title" in query
                assert "description" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_get_journey_by_id_data(self, app, mock_execute_query, sample_journey):
        """Test retrieving journey by ID at data layer"""
        with app.app_context():
            mock_execute_query.return_value = sample_journey
            
            try:
                from data.journey_data import get_journey_by_id
                
                journey = get_journey_by_id(journey_id=1)
                
                assert journey['id'] == 1
                assert journey['title'] == 'Test Journey'
                mock_execute_query.assert_called_once()
                
                # Verify SQL query
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "SELECT" in query
                assert "FROM journeys" in query
                assert "WHERE id" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_update_journey_data(self, app, mock_execute_query):
        """Test journey update at data layer"""
        with app.app_context():
            mock_execute_query.return_value = True
            
            try:
                from data.journey_data import update_journey
                
                success = update_journey(
                    journey_id=1,
                    title="Updated Journey",
                    description="Updated Description",
                    visibility="public"
                )
                
                assert success is True
                mock_execute_query.assert_called_once()
                
                # Verify SQL query
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "UPDATE journeys" in query
                assert "SET" in query
                assert "WHERE id" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_delete_journey_data(self, app, mock_execute_query):
        """Test journey deletion at data layer"""
        with app.app_context():
            mock_execute_query.return_value = True
            
            try:
                from data.journey_data import delete_journey
                
                success = delete_journey(journey_id=1)
                
                assert success is True
                mock_execute_query.assert_called_once()
                
                # Verify SQL query
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "DELETE FROM journeys" in query
                assert "WHERE id" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_get_user_journeys_data(self, app, mock_execute_query, sample_journey):
        """Test retrieving user journeys at data layer"""
        with app.app_context():
            mock_journeys = [sample_journey]
            mock_execute_query.return_value = mock_journeys
            
            try:
                from data.journey_data import get_user_journeys
                
                journeys = get_user_journeys(user_id=1)
                
                assert len(journeys) == 1
                assert journeys[0]['title'] == 'Test Journey'
                mock_execute_query.assert_called_once()
                
                # Verify SQL query
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "SELECT" in query
                assert "FROM journeys" in query
                assert "WHERE user_id" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_get_public_journeys_data(self, app, mock_execute_query, sample_journey):
        """Test retrieving public journeys at data layer"""
        with app.app_context():
            public_journey = {**sample_journey, 'visibility': 'public'}
            mock_execute_query.return_value = [public_journey]
            
            try:
                from data.journey_data import get_public_journeys
                
                journeys = get_public_journeys()
                
                assert len(journeys) == 1
                assert journeys[0]['visibility'] == 'public'
                mock_execute_query.assert_called_once()
                
                # Verify SQL query
                call_args = mock_execute_query.call_args
                query = call_args[0][0]
                assert "SELECT" in query
                assert "FROM journeys" in query
                assert "visibility" in query or "public" in query
                
            except ImportError:
                pytest.skip("Journey data module not found")


@pytest.mark.unit
@pytest.mark.journey
@pytest.mark.database
class TestJourneyDataValidation:
    """Test data layer validation and error handling"""
    
    def test_create_journey_invalid_data(self, app, mock_execute_query):
        """Test journey creation with invalid data"""
        with app.app_context():
            # Mock database error
            mock_execute_query.side_effect = Exception("Database error")
            
            try:
                from data.journey_data import create_journey
                
                with pytest.raises(Exception):
                    create_journey(
                        user_id=None,  # Invalid user_id
                        title="",      # Empty title
                        description="Test",
                        start_date=None,  # Invalid date
                        visibility="invalid"  # Invalid visibility
                    )
                    
            except ImportError:
                pytest.skip("Journey data module not found")
    
    def test_database_connection_error(self, app):
        """Test handling of database connection errors"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_conn:
                mock_conn.side_effect = Exception("Connection failed")
                
                try:
                    from data.journey_data import get_journey_by_id
                    
                    with pytest.raises(Exception):
                        get_journey_by_id(journey_id=1)
                        
                except ImportError:
                    pytest.skip("Journey data module not found")
    
    def test_sql_injection_prevention(self, app, mock_execute_query):
        """Test that SQL injection is prevented"""
        with app.app_context():
            mock_execute_query.return_value = None
            
            try:
                from data.journey_data import get_journey_by_id
                
                # Attempt SQL injection
                malicious_id = "1; DROP TABLE journeys; --"
                get_journey_by_id(journey_id=malicious_id)
                
                # Verify parameterized query was used
                mock_execute_query.assert_called_once()
                call_args = mock_execute_query.call_args
                
                # Should use parameters, not string concatenation
                assert len(call_args) >= 2  # Query and parameters
                params = call_args[0][1] if len(call_args[0]) > 1 else call_args[1].get('params')
                assert params is not None
                
            except ImportError:
                pytest.skip("Journey data module not found")
