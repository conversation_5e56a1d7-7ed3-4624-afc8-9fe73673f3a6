"""
Unit tests for authentication routes

Tests the authentication blueprint which handles:
- User registration (GET/POST /register)
- User login (GET/POST /login)
- User logout (GET /logout)
- Session management and redirects
"""

import pytest
from flask import url_for

@pytest.mark.unit
@pytest.mark.auth
class TestAuthRoutes:
    """Test authentication route handlers"""
    
    def test_register_get(self, client):
        """Test GET request to registration page"""
        response = client.get('/register')
        
        # Should return registration form
        assert response.status_code == 200
        assert b'register' in response.data.lower()
        assert b'username' in response.data.lower()
        assert b'email' in response.data.lower()
        assert b'password' in response.data.lower()
    
    def test_register_post_valid(self, client, mock_auth_service):
        """Test POST request to register with valid data"""
        # Mock successful registration
        mock_auth_service.register_user.return_value = (True, "Registration successful", 123)
        
        response = client.post('/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'validpassword123',
            'confirm_password': 'validpassword123',
            'first_name': 'New',
            'last_name': 'User'
        })
        
        # Should redirect after successful registration
        assert response.status_code in [200, 302]
        mock_auth_service.register_user.assert_called_once()
    
    def test_register_post_invalid(self, client, mock_auth_service):
        """Test POST request to register with invalid data"""
        # Mock registration failure
        mock_auth_service.register_user.return_value = (False, "Username already exists", None)
        
        response = client.post('/register', data={
            'username': 'existinguser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'password123'
        })
        
        # Should return form with error
        assert response.status_code == 200
        mock_auth_service.register_user.assert_called_once()
    
    def test_login_get(self, client):
        """Test GET request to login page"""
        response = client.get('/login')
        
        # Should return login form
        assert response.status_code == 200
        assert b'login' in response.data.lower()
        assert b'username' in response.data.lower()
        assert b'password' in response.data.lower()
    
    def test_login_post_valid(self, client, mock_auth_service, sample_user):
        """Test POST request to login with valid credentials"""
        # Mock successful authentication
        mock_auth_service.authenticate_user.return_value = (True, "Login successful", sample_user)
        
        response = client.post('/login', data={
            'username': 'testuser',
            'password': 'correctpassword'
        })
        
        # Should redirect after successful login
        assert response.status_code in [200, 302]
        mock_auth_service.authenticate_user.assert_called_once_with(
            username='testuser',
            password='correctpassword'
        )
    
    def test_login_post_invalid(self, client, mock_auth_service):
        """Test POST request to login with invalid credentials"""
        # Mock authentication failure
        mock_auth_service.authenticate_user.return_value = (False, "Invalid username or password", None)
        
        response = client.post('/login', data={
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        # Should return form with error
        assert response.status_code == 200
        assert b'invalid' in response.data.lower() or b'error' in response.data.lower()
        mock_auth_service.authenticate_user.assert_called_once()
    
    def test_login_already_authenticated(self, authenticated_session):
        """Test login page when user is already authenticated"""
        client = authenticated_session
        
        response = client.get('/login')
        
        # Should redirect to dashboard
        assert response.status_code == 302
    
    def test_logout(self, authenticated_session):
        """Test logout functionality"""
        client = authenticated_session
        
        response = client.get('/logout')
        
        # Should redirect to landing page
        assert response.status_code == 302
        
        # Session should be cleared (test by accessing a protected route)
        protected_response = client.get('/journey/private')
        assert protected_response.status_code == 302  # Should redirect to login
    
    def test_register_already_authenticated(self, authenticated_session):
        """Test register page when user is already authenticated"""
        client = authenticated_session
        
        response = client.get('/register')
        
        # Should redirect to dashboard
        assert response.status_code == 302


@pytest.mark.unit
@pytest.mark.auth
class TestAuthenticationFlow:
    """Test complete authentication workflows"""
    
    def test_complete_registration_flow(self, client, mock_auth_service):
        """Test complete user registration workflow"""
        # Mock successful registration
        mock_auth_service.register_user.return_value = (True, "Registration successful", 123)
        
        # Step 1: Get registration form
        response = client.get('/register')
        assert response.status_code == 200
        
        # Step 2: Submit registration
        response = client.post('/register', data={
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'password123',
            'first_name': 'New',
            'last_name': 'User',
            'location': 'Test City'
        })
        
        # Should redirect or show success
        assert response.status_code in [200, 302]
        mock_auth_service.register_user.assert_called_once()
    
    def test_complete_login_flow(self, client, mock_auth_service, sample_user):
        """Test complete user login workflow"""
        # Mock successful authentication
        mock_auth_service.authenticate_user.return_value = (True, "Login successful", sample_user)
        
        # Step 1: Get login form
        response = client.get('/login')
        assert response.status_code == 200
        
        # Step 2: Submit login
        response = client.post('/login', data={
            'username': 'testuser',
            'password': 'password123'
        })
        
        # Should redirect to dashboard
        assert response.status_code in [200, 302]
        mock_auth_service.authenticate_user.assert_called_once()
    
    def test_login_logout_cycle(self, client, mock_auth_service, sample_user):
        """Test login followed by logout"""
        # Mock successful authentication
        mock_auth_service.authenticate_user.return_value = (True, "Login successful", sample_user)
        
        # Login
        response = client.post('/login', data={
            'username': 'testuser',
            'password': 'password123'
        })
        assert response.status_code in [200, 302]
        
        # Logout
        response = client.get('/logout')
        assert response.status_code == 302
        
        # Verify logout by accessing protected route
        response = client.get('/journey/private')
        assert response.status_code == 302  # Should redirect to login


@pytest.mark.unit
@pytest.mark.auth
class TestAuthenticationSecurity:
    """Test authentication security features"""
    
    def test_password_validation(self, client, mock_auth_service):
        """Test password validation during registration"""
        # Mock registration failure due to weak password
        mock_auth_service.register_user.return_value = (False, "Password too weak", None)
        
        response = client.post('/register', data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': '123',  # Weak password
            'confirm_password': '123'
        })
        
        assert response.status_code == 200
        mock_auth_service.register_user.assert_called_once()
    
    def test_password_confirmation_mismatch(self, client, mock_auth_service):
        """Test password confirmation validation"""
        # Mock registration failure due to password mismatch
        mock_auth_service.register_user.return_value = (False, "Passwords do not match", None)
        
        response = client.post('/register', data={
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'password123',
            'confirm_password': 'different123'  # Mismatched confirmation
        })
        
        assert response.status_code == 200
        mock_auth_service.register_user.assert_called_once()
    
    def test_banned_user_login(self, client, mock_auth_service):
        """Test login attempt by banned user"""
        # Mock authentication failure for banned user
        mock_auth_service.authenticate_user.return_value = (
            False, 
            "Your account has been banned. You can submit an appeal by <a href='/helpdesk/ban-appeal/testuser' target='_blank'>clicking here</a>.", 
            None
        )
        
        response = client.post('/login', data={
            'username': 'banneduser',
            'password': 'password123'
        })
        
        assert response.status_code == 200
        assert b'banned' in response.data.lower()
        mock_auth_service.authenticate_user.assert_called_once()
