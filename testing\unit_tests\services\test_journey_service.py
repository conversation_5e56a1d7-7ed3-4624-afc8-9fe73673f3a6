"""
Unit tests for journey service functions

Tests the journey service layer which handles:
- Journey creation, retrieval, updating, and deletion
- Journey visibility and permission management
- Journey search and filtering
- Public/private journey management
"""

import pytest
from datetime import date, datetime
from unittest.mock import MagicMock, patch

@pytest.mark.unit
@pytest.mark.journey
class TestJourneyService:
    """Test journey service functions based on actual Footprints webapp structure"""
    
    def test_create_journey_success(self, app, mock_execute_query, sample_user):
        """Test successful journey creation"""
        with app.app_context():
            # Mock successful journey creation
            mock_execute_query.return_value = 123  # Mock journey ID

            # Import your journey service
            from services.journey_service import create_journey

            # Test journey creation (using actual function signature)
            success, message, journey_id = create_journey(
                user_id=sample_user['id'],
                title="Test Journey",
                description="Test Description",
                start_date="2025-01-01",
                visibility="private"  # Use visibility instead of is_public
            )

            # Verify results
            assert success is True
            assert "success" in message.lower()
            assert journey_id == 123
    
    def test_create_journey_blocked_user(self, app, mock_user_service):
        """Test journey creation with blocked user"""
        with app.app_context():
            # Mock blocked user
            mock_user_service.get_user_by_id.return_value = {'is_blocked': True}

            from services.journey_service import create_journey

            # Test journey creation with blocked user (using actual function signature)
            success, message, journey_id = create_journey(
                user_id=1,
                title="Test Journey",
                description="Test Description",
                start_date="2025-01-01",
                visibility="private"
            )

            # Should fail for blocked user
            assert success is False
            assert "blocked" in message.lower()
            assert journey_id is None
    
    def test_get_private_journeys(self, app, mock_execute_query, sample_journey):
        """Test retrieving user's private journeys"""
        with app.app_context():
            # Mock journey data
            mock_journeys = [
                sample_journey,
                {
                    **sample_journey,
                    'id': 2,
                    'title': 'Journey 2',
                    'description': 'Description 2'
                }
            ]
            mock_execute_query.return_value = mock_journeys

            from services.journey_service import get_private_journeys

            # Test getting private journeys (using actual function name)
            journeys = get_private_journeys(user_id=1)

            # Verify results
            assert len(journeys) == 2
            assert journeys[0]['title'] == 'Test Journey'
            assert journeys[1]['title'] == 'Journey 2'
    
    def test_get_journey(self, app, mock_execute_query, sample_journey):
        """Test retrieving journey by ID"""
        with app.app_context():
            mock_execute_query.return_value = sample_journey

            from services.journey_service import get_journey

            # Test getting journey by ID (using actual function name and signature)
            success, message, journey = get_journey(journey_id=1, user_id=1)

            # Verify results
            assert success is True
            assert journey['id'] == 1
            assert journey['title'] == 'Test Journey'
    
    def test_update_journey(self, app, mock_execute_query):
        """Test updating journey"""
        with app.app_context():
            mock_execute_query.return_value = True  # Mock successful update

            from services.journey_service import update_journey

            # Test journey update (using actual function signature)
            success, message = update_journey(
                journey_id=1,
                user_id=1,
                title="Updated Journey",
                description="Updated Description",
                start_date="2025-02-01",
                visibility="public"  # Use visibility instead of is_public
            )

            # Verify results
            assert success is True
            assert "success" in message.lower()
    
    def test_delete_journey(self, app, mock_execute_query):
        """Test deleting journey"""
        with app.app_context():
            mock_execute_query.return_value = True  # Mock successful deletion

            from services.journey_service import delete_journey

            # Test journey deletion (function signature is correct)
            success, message = delete_journey(journey_id=1, user_id=1)

            # Verify results
            assert success is True
            assert "success" in message.lower()
    
    def test_get_public_journeys(self, app, mock_execute_query, sample_journey):
        """Test retrieving public journeys"""
        with app.app_context():
            # Mock public journey data
            public_journey = {**sample_journey, 'visibility': 'public'}
            mock_execute_query.return_value = [public_journey]

            from services.journey_service import get_public_journeys

            # Test getting public journeys (function signature allows optional parameters)
            journeys = get_public_journeys()

            # Verify results
            assert len(journeys) == 1
            assert journeys[0]['visibility'] == 'public'


@pytest.mark.unit
@pytest.mark.journey
class TestJourneyValidation:
    """Test journey validation functions"""
    
    def test_validate_journey_data_valid(self):
        """Test journey data validation with valid data"""
        try:
            from services.journey_service import validate_journey_data
        except ImportError:
            pytest.skip("Journey validation not found")
        
        # Test with valid data
        is_valid, errors = validate_journey_data(
            title="Valid Journey",
            description="Valid Description",
            start_date="2025-01-01"
        )
        
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_journey_data_invalid(self):
        """Test journey data validation with invalid data"""
        try:
            from services.journey_service import validate_journey_data
        except ImportError:
            pytest.skip("Journey validation not found")
        
        # Test with invalid data
        is_valid, errors = validate_journey_data(
            title="",  # Empty title
            description="Valid Description",
            start_date="invalid-date"  # Invalid date
        )
        
        assert is_valid is False
        assert len(errors) > 0
        assert any("title" in error.lower() for error in errors)
        assert any("date" in error.lower() for error in errors)
