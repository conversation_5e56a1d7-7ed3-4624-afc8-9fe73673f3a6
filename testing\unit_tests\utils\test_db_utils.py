"""
Unit tests for database utilities

Tests the database utility functions that handle connection management,
query execution, and database operations.
"""

import pytest
from unittest.mock import patch, MagicMock
import mysql.connector

@pytest.mark.unit
@pytest.mark.database
class TestDatabaseUtils:
    """Test database utility functions"""
    
    def test_get_db_connection(self, app):
        """Test database connection creation"""
        with app.app_context():
            with patch('mysql.connector.connect') as mock_connect:
                mock_connection = MagicMock()
                mock_connect.return_value = mock_connection
                
                try:
                    from utils.db_utils import get_db_connection
                    
                    connection = get_db_connection()
                    
                    assert connection == mock_connection
                    mock_connect.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_execute_query_select(self, app):
        """Test execute_query for SELECT operations"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                # Mock connection and cursor
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock query results
                mock_cursor.fetchall.return_value = [
                    {'id': 1, 'title': 'Test Journey'},
                    {'id': 2, 'title': 'Another Journey'}
                ]
                
                try:
                    from utils.db_utils import execute_query
                    
                    result = execute_query(
                        "SELECT * FROM journeys WHERE user_id = %s",
                        params=(1,),
                        fetch_all=True
                    )
                    
                    assert len(result) == 2
                    assert result[0]['title'] == 'Test Journey'
                    
                    # Verify cursor operations
                    mock_cursor.execute.assert_called_once()
                    mock_cursor.fetchall.assert_called_once()
                    mock_connection.close.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_execute_query_insert(self, app):
        """Test execute_query for INSERT operations"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock insert result
                mock_cursor.lastrowid = 123
                
                try:
                    from utils.db_utils import execute_query
                    
                    result = execute_query(
                        "INSERT INTO journeys (title, user_id) VALUES (%s, %s)",
                        params=("Test Journey", 1),
                        commit=True
                    )
                    
                    assert result == 123
                    
                    # Verify operations
                    mock_cursor.execute.assert_called_once()
                    mock_connection.commit.assert_called_once()
                    mock_connection.close.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_execute_query_update(self, app):
        """Test execute_query for UPDATE operations"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock update result
                mock_cursor.rowcount = 1
                
                try:
                    from utils.db_utils import execute_query
                    
                    result = execute_query(
                        "UPDATE journeys SET title = %s WHERE id = %s",
                        params=("Updated Title", 1),
                        commit=True
                    )
                    
                    assert result == 1
                    
                    # Verify operations
                    mock_cursor.execute.assert_called_once()
                    mock_connection.commit.assert_called_once()
                    mock_connection.close.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_execute_query_fetch_one(self, app):
        """Test execute_query with fetch_one option"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock single result
                mock_cursor.fetchone.return_value = {'id': 1, 'title': 'Test Journey'}
                
                try:
                    from utils.db_utils import execute_query
                    
                    result = execute_query(
                        "SELECT * FROM journeys WHERE id = %s",
                        params=(1,),
                        fetch_one=True
                    )
                    
                    assert result['id'] == 1
                    assert result['title'] == 'Test Journey'
                    
                    # Verify cursor operations
                    mock_cursor.execute.assert_called_once()
                    mock_cursor.fetchone.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")


@pytest.mark.unit
@pytest.mark.database
class TestDatabaseErrorHandling:
    """Test database error handling"""
    
    def test_connection_error_handling(self, app):
        """Test handling of database connection errors"""
        with app.app_context():
            with patch('mysql.connector.connect') as mock_connect:
                mock_connect.side_effect = mysql.connector.Error("Connection failed")
                
                try:
                    from utils.db_utils import get_db_connection
                    
                    with pytest.raises(mysql.connector.Error):
                        get_db_connection()
                        
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_query_execution_error(self, app):
        """Test handling of query execution errors"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock query execution error
                mock_cursor.execute.side_effect = mysql.connector.Error("Query failed")
                
                try:
                    from utils.db_utils import execute_query
                    
                    with pytest.raises(mysql.connector.Error):
                        execute_query("INVALID SQL QUERY")
                        
                    # Verify connection is still closed
                    mock_connection.close.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")
    
    def test_transaction_rollback(self, app):
        """Test transaction rollback on error"""
        with app.app_context():
            with patch('utils.db_utils.get_db_connection') as mock_get_conn:
                mock_connection = MagicMock()
                mock_cursor = MagicMock()
                mock_connection.cursor.return_value = mock_cursor
                mock_get_conn.return_value = mock_connection
                
                # Mock commit error
                mock_connection.commit.side_effect = mysql.connector.Error("Commit failed")
                
                try:
                    from utils.db_utils import execute_query
                    
                    with pytest.raises(mysql.connector.Error):
                        execute_query(
                            "INSERT INTO journeys (title) VALUES (%s)",
                            params=("Test",),
                            commit=True
                        )
                    
                    # Verify rollback was called
                    mock_connection.rollback.assert_called_once()
                    mock_connection.close.assert_called_once()
                    
                except ImportError:
                    pytest.skip("Database utils module not found")


@pytest.mark.unit
class TestSecurityUtils:
    """Test security utility functions"""
    
    def test_password_hashing(self, app):
        """Test password hashing functionality"""
        with app.app_context():
            try:
                from utils.security import hash_password, check_password
                
                password = "testpassword123"
                hashed = hash_password(password)
                
                # Verify hash is different from original
                assert hashed != password
                assert len(hashed) > 20  # Reasonable hash length
                
                # Verify password checking
                assert check_password(password, hashed) is True
                assert check_password("wrongpassword", hashed) is False
                
            except ImportError:
                pytest.skip("Security utils module not found")
    
    def test_login_required_decorator(self, app, client):
        """Test login_required decorator"""
        with app.app_context():
            try:
                from utils.security import login_required
                from flask import session
                
                # Test decorator exists and is callable
                assert callable(login_required)
                
                # Test with unauthenticated request
                response = client.get('/journey/private')
                assert response.status_code == 302  # Should redirect to login
                
            except ImportError:
                pytest.skip("Security utils module not found")


@pytest.mark.unit
class TestFileUtils:
    """Test file utility functions"""
    
    def test_allowed_file_validation(self, app):
        """Test file extension validation"""
        with app.app_context():
            try:
                from utils.file_utils import allowed_file
                
                # Test valid image files
                assert allowed_file("test.jpg") is True
                assert allowed_file("test.jpeg") is True
                assert allowed_file("test.png") is True
                assert allowed_file("test.gif") is True
                
                # Test invalid files
                assert allowed_file("test.txt") is False
                assert allowed_file("test.exe") is False
                assert allowed_file("test") is False
                
            except ImportError:
                pytest.skip("File utils module not found")
    
    def test_secure_filename_generation(self, app):
        """Test secure filename generation"""
        with app.app_context():
            try:
                from utils.file_utils import secure_filename
                
                # Test filename sanitization
                safe_name = secure_filename("../../../etc/passwd")
                assert "../" not in safe_name
                assert "etc" not in safe_name
                
                # Test normal filename
                normal_name = secure_filename("my_image.jpg")
                assert normal_name == "my_image.jpg"
                
            except ImportError:
                pytest.skip("File utils module not found")
